// Auto generated code by esphome
// ========== AUTO GENERATED INCLUDE BLOCK BEGIN ===========
#include "esphome.h"
using namespace esphome;
using std::isnan;
using std::min;
using std::max;
using namespace binary_sensor;
using namespace switch_;
using namespace button;
using namespace sensor;
using namespace text_sensor;
using namespace number;
using namespace light;
logger::Logger *logger_logger_id;
web_server_base::WebServerBase *web_server_base_webserverbase_id;
captive_portal::CaptivePortal *captive_portal_captiveportal_id;
wifi::WiFiComponent *wifi_wificomponent_id;
mdns::MDNSComponent *mdns_mdnscomponent_id;
web_server::WebServerOTAComponent *web_server_webserverotacomponent_id;
esphome::ESPHomeOTAComponent *esphome_esphomeotacomponent_id;
safe_mode::SafeModeComponent *safe_mode_safemodecomponent_id;
api::APIServer *api_apiserver_id;
using namespace api;
web_server::WebServer *web_server_webserver_id;
const uint8_t ESPHOME_WEBSERVER_INDEX_HTML[174] PROGMEM = {60, 33, 68, 79, 67, 84, 89, 80, 69, 32, 104, 116, 109, 108, 62, 60, 104, 116, 109, 108, 62, 60, 104, 101, 97, 100, 62, 60, 109, 101, 116, 97, 32, 99, 104, 97, 114, 115, 101, 116, 61, 85, 84, 70, 45, 56, 62, 60, 108, 105, 110, 107, 32, 114, 101, 108, 61, 105, 99, 111, 110, 32, 104, 114, 101, 102, 61, 100, 97, 116, 97, 58, 62, 60, 47, 104, 101, 97, 100, 62, 60, 98, 111, 100, 121, 62, 60, 101, 115, 112, 45, 97, 112, 112, 62, 60, 47, 101, 115, 112, 45, 97, 112, 112, 62, 60, 115, 99, 114, 105, 112, 116, 32, 115, 114, 99, 61, 34, 104, 116, 116, 112, 115, 58, 47, 47, 111, 105, 46, 101, 115, 112, 104, 111, 109, 101, 46, 105, 111, 47, 118, 50, 47, 119, 119, 119, 46, 106, 115, 34, 62, 60, 47, 115, 99, 114, 105, 112, 116, 62, 60, 47, 98, 111, 100, 121, 62, 60, 47, 104, 116, 109, 108, 62};
const size_t ESPHOME_WEBSERVER_INDEX_HTML_SIZE = 174;
using namespace json;
preferences::IntervalSyncer *preferences_intervalsyncer_id;
status::StatusBinarySensor *status_statusbinarysensor_id;
restart::RestartSwitch *restart_restartswitch_id;
template_::TemplateSwitch *led_power;
Automation<> *automation_id_2;
template_::TemplateSwitch *auto_detect_leds;
Automation<> *automation_id_4;
LambdaAction<> *lambdaaction_id_2;
Automation<> *automation_id_3;
template_::TemplateButton *start_led_scan;
button::ButtonPressTrigger *button_buttonpresstrigger_id;
Automation<> *automation_id_5;
template_::TemplateButton *template__templatebutton_id;
button::ButtonPressTrigger *button_buttonpresstrigger_id_2;
Automation<> *automation_id_6;
template_::TemplateButton *template__templatebutton_id_2;
button::ButtonPressTrigger *button_buttonpresstrigger_id_3;
Automation<> *automation_id_7;
template_::TemplateButton *template__templatebutton_id_3;
button::ButtonPressTrigger *button_buttonpresstrigger_id_4;
Automation<> *automation_id_8;
template_::TemplateButton *template__templatebutton_id_4;
button::ButtonPressTrigger *button_buttonpresstrigger_id_5;
Automation<> *automation_id_9;
template_::TemplateButton *template__templatebutton_id_5;
button::ButtonPressTrigger *button_buttonpresstrigger_id_6;
Automation<> *automation_id_10;
script::RestartScript<> *led_scan_detect;
script::SingleScript<> *show_current_leds;
script::SingleScript<int> *confirm_led_count;
Automation<> *automation_id_11;
LambdaAction<> *lambdaaction_id_3;
wifi_signal::WiFiSignalSensor *wifi_signal_wifisignalsensor_id;
uptime::UptimeSecondsSensor *uptime_uptimesecondssensor_id;
wifi_info::SSIDWiFiInfo *wifi_info_ssidwifiinfo_id;
wifi_info::MacAddressWifiInfo *wifi_info_macaddresswifiinfo_id;
wifi_info::IPAddressWiFiInfo *wifi_info_ipaddresswifiinfo_id;
template_::TemplateNumber *led_count;
number::NumberStateTrigger *number_numberstatetrigger_id;
Automation<float> *automation_id_14;
fastled_base::FastLEDLightOutput *fastled_base_fastledlightoutput_id;
light::AddressableLightState *led_strip;
light::AddressableRainbowLightEffect *light_addressablerainbowlighteffect_id;
light::AddressableColorWipeEffect *light_addressablecolorwipeeffect_id;
light::AddressableScanEffect *light_addressablescaneffect_id;
light::AddressableTwinkleEffect *light_addressabletwinkleeffect_id;
light::AddressableRandomTwinkleEffect *light_addressablerandomtwinkleeffect_id;
light::AddressableFireworksEffect *light_addressablefireworkseffect_id;
light::AddressableFlickerEffect *light_addressableflickereffect_id;
light::LightControlAction<> *light_lightcontrolaction_id_2;
Automation<> *automation_id;
light::LightControlAction<> *light_lightcontrolaction_id;
LambdaAction<> *lambdaaction_id;
script::ScriptExecuteAction<script::Script<>> *script_scriptexecuteaction_id;
script::ScriptExecuteAction<script::Script<int>> *script_scriptexecuteaction_id_2;
script::ScriptExecuteAction<script::Script<int>> *script_scriptexecuteaction_id_3;
script::ScriptExecuteAction<script::Script<int>> *script_scriptexecuteaction_id_4;
script::ScriptExecuteAction<script::Script<int>> *script_scriptexecuteaction_id_5;
script::ScriptExecuteAction<script::Script<>> *script_scriptexecuteaction_id_6;
light::LightControlAction<> *light_lightcontrolaction_id_3;
DelayAction<> *delayaction_id;
RepeatAction<> *repeataction_id;
LambdaAction<uint32_t> *lambdaaction_id_4;
light::AddressableSet<uint32_t> *light_addressableset_id;
light::AddressableSet<uint32_t> *light_addressableset_id_2;
DelayAction<uint32_t> *delayaction_id_2;
Automation<> *automation_id_12;
LambdaAction<> *lambdaaction_id_5;
light::AddressableSet<> *light_addressableset_id_3;
light::AddressableSet<> *light_addressableset_id_4;
DelayAction<> *delayaction_id_3;
light::AddressableSet<> *light_addressableset_id_5;
Automation<int> *automation_id_13;
LambdaAction<int> *lambdaaction_id_6;
RepeatAction<int> *repeataction_id_2;
light::AddressableSet<uint32_t, int> *light_addressableset_id_6;
DelayAction<uint32_t, int> *delayaction_id_4;
light::AddressableSet<uint32_t, int> *light_addressableset_id_7;
DelayAction<uint32_t, int> *delayaction_id_5;
LambdaAction<float> *lambdaaction_id_7;
#define yield() esphome::yield()
#define millis() esphome::millis()
#define micros() esphome::micros()
#define delay(x) esphome::delay(x)
#define delayMicroseconds(x) esphome::delayMicroseconds(x)
// ========== AUTO GENERATED INCLUDE BLOCK END ==========="

void setup() {
  // ========== AUTO GENERATED CODE BEGIN ===========
  App.reserve_text_sensor(3);
  App.reserve_switch(3);
  App.reserve_sensor(2);
  App.reserve_number(1);
  App.reserve_light(1);
  App.reserve_button(6);
  App.reserve_binary_sensor(1);
  // network:
  //   enable_ipv6: false
  //   min_ipv6_addr_count: 0
  // async_tcp:
  //   {}
  // esphome:
  //   name: led-myesp32c3
  //   friendly_name: Fastled ESP32-C3
  //   min_version: 2025.7.2
  //   build_path: build\led-myesp32c3
  //   platformio_options: {}
  //   includes: []
  //   libraries: []
  //   name_add_mac_suffix: false
  //   debug_scheduler: false
  //   areas: []
  //   devices: []
  App.pre_setup("led-myesp32c3", "Fastled ESP32-C3", "", __DATE__ ", " __TIME__, false);
  App.reserve_components(28);
  // binary_sensor:
  // switch:
  // button:
  // sensor:
  // text_sensor:
  // number:
  // light:
  // logger:
  //   baud_rate: 115200
  //   level: INFO
  //   logs:
  //     voc_uart: INFO
  //     uart_debug: WARN
  //     uart: WARN
  //     wifi: WARN
  //     api: WARN
  //     ota: WARN
  //     web_server: WARN
  //   id: logger_logger_id
  //   tx_buffer_size: 512
  //   deassert_rts_dtr: false
  //   task_log_buffer_size: 768
  //   hardware_uart: USB_CDC
  logger_logger_id = new logger::Logger(115200, 512);
  logger_logger_id->create_pthread_key();
  logger_logger_id->init_log_buffer(768);
  logger_logger_id->set_log_level(ESPHOME_LOG_LEVEL_INFO);
  logger_logger_id->set_uart_selection(logger::UART_SELECTION_USB_CDC);
  logger_logger_id->pre_setup();
  logger_logger_id->set_log_level("voc_uart", ESPHOME_LOG_LEVEL_INFO);
  logger_logger_id->set_log_level("uart_debug", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("uart", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("wifi", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("api", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("ota", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("web_server", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_component_source("logger");
  App.register_component(logger_logger_id);
  // web_server_base:
  //   id: web_server_base_webserverbase_id
  web_server_base_webserverbase_id = new web_server_base::WebServerBase();
  web_server_base_webserverbase_id->set_component_source("web_server_base");
  App.register_component(web_server_base_webserverbase_id);
  web_server_base::global_web_server_base = web_server_base_webserverbase_id;
  // captive_portal:
  //   id: captive_portal_captiveportal_id
  //   web_server_base_id: web_server_base_webserverbase_id
  captive_portal_captiveportal_id = new captive_portal::CaptivePortal(web_server_base_webserverbase_id);
  captive_portal_captiveportal_id->set_component_source("captive_portal");
  App.register_component(captive_portal_captiveportal_id);
  // wifi:
  //   ap:
  //     ssid: ESP32-C3 Fallback Hotspot
  //     password: '12345678'
  //     id: wifi_wifiap_id
  //     ap_timeout: 1min
  //   id: wifi_wificomponent_id
  //   domain: .local
  //   reboot_timeout: 15min
  //   power_save_mode: LIGHT
  //   fast_connect: false
  //   passive_scan: false
  //   enable_on_boot: true
  //   networks:
  //     - ssid: !secret 'wifi_ssid'
  //       password: !secret 'wifi_password'
  //       id: wifi_wifiap_id_2
  //       priority: 0.0
  //   use_address: led-myesp32c3.local
  wifi_wificomponent_id = new wifi::WiFiComponent();
  wifi_wificomponent_id->set_use_address("led-myesp32c3.local");
  {
  wifi::WiFiAP wifi_wifiap_id_2 = wifi::WiFiAP();
  wifi_wifiap_id_2.set_ssid("HOME");
  wifi_wifiap_id_2.set_password("nb9d30@24zd");
  wifi_wifiap_id_2.set_priority(0.0f);
  wifi_wificomponent_id->add_sta(wifi_wifiap_id_2);
  }
  {
  wifi::WiFiAP wifi_wifiap_id = wifi::WiFiAP();
  wifi_wifiap_id.set_ssid("ESP32-C3 Fallback Hotspot");
  wifi_wifiap_id.set_password("12345678");
  wifi_wificomponent_id->set_ap(wifi_wifiap_id);
  }
  wifi_wificomponent_id->set_ap_timeout(60000);
  wifi_wificomponent_id->set_reboot_timeout(900000);
  wifi_wificomponent_id->set_power_save_mode(wifi::WIFI_POWER_SAVE_LIGHT);
  wifi_wificomponent_id->set_fast_connect(false);
  wifi_wificomponent_id->set_passive_scan(false);
  wifi_wificomponent_id->set_enable_on_boot(true);
  wifi_wificomponent_id->set_component_source("wifi");
  App.register_component(wifi_wificomponent_id);
  // mdns:
  //   id: mdns_mdnscomponent_id
  //   disabled: false
  //   services: []
  mdns_mdnscomponent_id = new mdns::MDNSComponent();
  mdns_mdnscomponent_id->set_component_source("mdns");
  App.register_component(mdns_mdnscomponent_id);
  // ota:
  // ota.web_server:
  //   platform: web_server
  //   id: web_server_webserverotacomponent_id
  web_server_webserverotacomponent_id = new web_server::WebServerOTAComponent();
  // ota.esphome:
  //   platform: esphome
  //   id: esphome_esphomeotacomponent_id
  //   version: 2
  //   port: 3232
  esphome_esphomeotacomponent_id = new esphome::ESPHomeOTAComponent();
  esphome_esphomeotacomponent_id->set_port(3232);
  esphome_esphomeotacomponent_id->set_component_source("esphome.ota");
  App.register_component(esphome_esphomeotacomponent_id);
  // safe_mode:
  //   id: safe_mode_safemodecomponent_id
  //   boot_is_good_after: 1min
  //   disabled: false
  //   num_attempts: 10
  //   reboot_timeout: 5min
  safe_mode_safemodecomponent_id = new safe_mode::SafeModeComponent();
  safe_mode_safemodecomponent_id->set_component_source("safe_mode");
  App.register_component(safe_mode_safemodecomponent_id);
  if (safe_mode_safemodecomponent_id->should_enter_safe_mode(10, 300000, 60000)) return;
  web_server_webserverotacomponent_id->set_component_source("web_server.ota");
  App.register_component(web_server_webserverotacomponent_id);
  // api:
  //   id: api_apiserver_id
  //   port: 6053
  //   password: ''
  //   reboot_timeout: 15min
  //   batch_delay: 100ms
  //   custom_services: false
  api_apiserver_id = new api::APIServer();
  api_apiserver_id->set_component_source("api");
  App.register_component(api_apiserver_id);
  api_apiserver_id->set_port(6053);
  api_apiserver_id->set_reboot_timeout(900000);
  api_apiserver_id->set_batch_delay(100);
  // web_server:
  //   port: 80
  //   auth:
  //     username: admin
  //     password: !secret 'wifi_password'
  //   id: web_server_webserver_id
  //   version: 2
  //   enable_private_network_access: true
  //   web_server_base_id: web_server_base_webserverbase_id
  //   include_internal: false
  //   log: true
  //   css_url: ''
  //   js_url: https:oi.esphome.io/v2/www.js
  web_server_webserver_id = new web_server::WebServer(web_server_base_webserverbase_id);
  web_server_webserver_id->set_component_source("web_server");
  App.register_component(web_server_webserver_id);
  web_server_base_webserverbase_id->set_port(80);
  web_server_webserver_id->set_expose_log(true);
  web_server_base_webserverbase_id->set_auth_username("admin");
  web_server_base_webserverbase_id->set_auth_password("nb9d30@24zd");
  web_server_webserver_id->set_include_internal(false);
  // json:
  //   {}
  // esp32:
  //   board: airm2m_core_esp32c3
  //   framework:
  //     version: 3.1.3
  //     advanced:
  //       ignore_efuse_custom_mac: false
  //     source: pioarduino/framework-arduinoespressif32@https:github.com/espressif/arduino-esp32/releases/download/3.1.3/esp32-3.1.3.zip
  //     platform_version: https:github.com/pioarduino/platform-espressif32/releases/download/53.03.13/platform-espressif32.zip
  //     type: arduino
  //   flash_size: 4MB
  //   variant: ESP32C3
  //   cpu_frequency: 160MHZ
  setCpuFrequencyMhz(160);
  // preferences:
  //   id: preferences_intervalsyncer_id
  //   flash_write_interval: 60s
  preferences_intervalsyncer_id = new preferences::IntervalSyncer();
  preferences_intervalsyncer_id->set_write_interval(60000);
  preferences_intervalsyncer_id->set_component_source("preferences");
  App.register_component(preferences_intervalsyncer_id);
  // binary_sensor.status:
  //   platform: status
  //   name: Status
  //   disabled_by_default: false
  //   id: status_statusbinarysensor_id
  //   entity_category: diagnostic
  //   device_class: connectivity
  status_statusbinarysensor_id = new status::StatusBinarySensor();
  App.register_binary_sensor(status_statusbinarysensor_id);
  status_statusbinarysensor_id->set_name("Status");
  status_statusbinarysensor_id->set_object_id("status");
  status_statusbinarysensor_id->set_disabled_by_default(false);
  status_statusbinarysensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  status_statusbinarysensor_id->set_device_class("connectivity");
  status_statusbinarysensor_id->set_trigger_on_initial_state(false);
  status_statusbinarysensor_id->set_component_source("status.binary_sensor");
  App.register_component(status_statusbinarysensor_id);
  // switch.restart:
  //   platform: restart
  //   name: Restart
  //   disabled_by_default: false
  //   restore_mode: ALWAYS_OFF
  //   id: restart_restartswitch_id
  //   entity_category: config
  //   icon: mdi:restart
  restart_restartswitch_id = new restart::RestartSwitch();
  App.register_switch(restart_restartswitch_id);
  restart_restartswitch_id->set_name("Restart");
  restart_restartswitch_id->set_object_id("restart");
  restart_restartswitch_id->set_disabled_by_default(false);
  restart_restartswitch_id->set_icon("mdi:restart");
  restart_restartswitch_id->set_entity_category(::ENTITY_CATEGORY_CONFIG);
  restart_restartswitch_id->set_restore_mode(switch_::SWITCH_ALWAYS_OFF);
  restart_restartswitch_id->set_component_source("restart.switch");
  App.register_component(restart_restartswitch_id);
  // switch.template:
  //   platform: template
  //   name: LED Strip Power
  //   id: led_power
  //   optimistic: true
  //   turn_on_action:
  //     then:
  //       - light.turn_on:
  //           id: led_strip
  //           state: true
  //         type_id: light_lightcontrolaction_id
  //     trigger_id: trigger_id
  //     automation_id: automation_id
  //   turn_off_action:
  //     then:
  //       - light.turn_off:
  //           id: led_strip
  //           state: false
  //         type_id: light_lightcontrolaction_id_2
  //     trigger_id: trigger_id_2
  //     automation_id: automation_id_2
  //   disabled_by_default: false
  //   restore_mode: ALWAYS_OFF
  //   assumed_state: false
  led_power = new template_::TemplateSwitch();
  App.register_switch(led_power);
  led_power->set_name("LED Strip Power");
  led_power->set_object_id("led_strip_power");
  led_power->set_disabled_by_default(false);
  led_power->set_restore_mode(switch_::SWITCH_ALWAYS_OFF);
  led_power->set_component_source("template.switch");
  App.register_component(led_power);
  automation_id_2 = new Automation<>(led_power->get_turn_off_trigger());
  // switch.template:
  //   platform: template
  //   name: Auto Detect LED Count
  //   id: auto_detect_leds
  //   turn_on_action:
  //     then:
  //       - lambda: !lambda |-
  //            自动检测LED数量的逻辑
  //           ESP_LOGI("led_detect", "开始自动检测LED数量...");
  //   
  //            先关闭LED
  //           auto call = id(led_strip).make_call();
  //           call.set_state(false);
  //           call.perform();
  //   
  //            测试不同的LED数量 (从1到300)
  //           int detected_count = 30;  默认值
  //   
  //            这里可以实现更复杂的检测逻辑
  //            目前设置为用户手动输入的值
  //           detected_count = (int)id(led_count).state;
  //   
  //           ESP_LOGI("led_detect", "检测到LED数量: %d", detected_count);
  //   
  //            更新LED数量
  //           id(led_count).publish_state(detected_count);
  //         type_id: lambdaaction_id
  //     trigger_id: trigger_id_3
  //     automation_id: automation_id_3
  //   turn_off_action:
  //     then:
  //       - lambda: !lambda |-
  //           ESP_LOGI("led_detect", "停止LED检测");
  //         type_id: lambdaaction_id_2
  //     trigger_id: trigger_id_4
  //     automation_id: automation_id_4
  //   disabled_by_default: false
  //   restore_mode: ALWAYS_OFF
  //   optimistic: false
  //   assumed_state: false
  auto_detect_leds = new template_::TemplateSwitch();
  App.register_switch(auto_detect_leds);
  auto_detect_leds->set_name("Auto Detect LED Count");
  auto_detect_leds->set_object_id("auto_detect_led_count");
  auto_detect_leds->set_disabled_by_default(false);
  auto_detect_leds->set_restore_mode(switch_::SWITCH_ALWAYS_OFF);
  auto_detect_leds->set_component_source("template.switch");
  App.register_component(auto_detect_leds);
  automation_id_4 = new Automation<>(auto_detect_leds->get_turn_off_trigger());
  lambdaaction_id_2 = new LambdaAction<>([=]() -> void {
      #line 60 "led-esp32c3.yaml"
      ESP_LOGI("led_detect", "停止LED检测");
  });
  automation_id_4->add_actions({lambdaaction_id_2});
  automation_id_3 = new Automation<>(auto_detect_leds->get_turn_on_trigger());
  // button.template:
  //   platform: template
  //   name: Start LED Scan
  //   id: start_led_scan
  //   on_press:
  //     - then:
  //         - script.execute:
  //             id: led_scan_detect
  //           type_id: script_scriptexecuteaction_id
  //       automation_id: automation_id_5
  //       trigger_id: button_buttonpresstrigger_id
  //   disabled_by_default: false
  start_led_scan = new template_::TemplateButton();
  App.register_button(start_led_scan);
  start_led_scan->set_name("Start LED Scan");
  start_led_scan->set_object_id("start_led_scan");
  start_led_scan->set_disabled_by_default(false);
  button_buttonpresstrigger_id = new button::ButtonPressTrigger(start_led_scan);
  automation_id_5 = new Automation<>(button_buttonpresstrigger_id);
  // button.template:
  //   platform: template
  //   name: Confirm 10 LEDs
  //   on_press:
  //     - then:
  //         - script.execute:
  //             id: confirm_led_count
  //             count: 10
  //           type_id: script_scriptexecuteaction_id_2
  //       automation_id: automation_id_6
  //       trigger_id: button_buttonpresstrigger_id_2
  //   disabled_by_default: false
  //   id: template__templatebutton_id
  template__templatebutton_id = new template_::TemplateButton();
  App.register_button(template__templatebutton_id);
  template__templatebutton_id->set_name("Confirm 10 LEDs");
  template__templatebutton_id->set_object_id("confirm_10_leds");
  template__templatebutton_id->set_disabled_by_default(false);
  button_buttonpresstrigger_id_2 = new button::ButtonPressTrigger(template__templatebutton_id);
  automation_id_6 = new Automation<>(button_buttonpresstrigger_id_2);
  // button.template:
  //   platform: template
  //   name: Confirm 30 LEDs
  //   on_press:
  //     - then:
  //         - script.execute:
  //             id: confirm_led_count
  //             count: 30
  //           type_id: script_scriptexecuteaction_id_3
  //       automation_id: automation_id_7
  //       trigger_id: button_buttonpresstrigger_id_3
  //   disabled_by_default: false
  //   id: template__templatebutton_id_2
  template__templatebutton_id_2 = new template_::TemplateButton();
  App.register_button(template__templatebutton_id_2);
  template__templatebutton_id_2->set_name("Confirm 30 LEDs");
  template__templatebutton_id_2->set_object_id("confirm_30_leds");
  template__templatebutton_id_2->set_disabled_by_default(false);
  button_buttonpresstrigger_id_3 = new button::ButtonPressTrigger(template__templatebutton_id_2);
  automation_id_7 = new Automation<>(button_buttonpresstrigger_id_3);
  // button.template:
  //   platform: template
  //   name: Confirm 60 LEDs
  //   on_press:
  //     - then:
  //         - script.execute:
  //             id: confirm_led_count
  //             count: 60
  //           type_id: script_scriptexecuteaction_id_4
  //       automation_id: automation_id_8
  //       trigger_id: button_buttonpresstrigger_id_4
  //   disabled_by_default: false
  //   id: template__templatebutton_id_3
  template__templatebutton_id_3 = new template_::TemplateButton();
  App.register_button(template__templatebutton_id_3);
  template__templatebutton_id_3->set_name("Confirm 60 LEDs");
  template__templatebutton_id_3->set_object_id("confirm_60_leds");
  template__templatebutton_id_3->set_disabled_by_default(false);
  button_buttonpresstrigger_id_4 = new button::ButtonPressTrigger(template__templatebutton_id_3);
  automation_id_8 = new Automation<>(button_buttonpresstrigger_id_4);
  // button.template:
  //   platform: template
  //   name: Confirm 144 LEDs
  //   on_press:
  //     - then:
  //         - script.execute:
  //             id: confirm_led_count
  //             count: 144
  //           type_id: script_scriptexecuteaction_id_5
  //       automation_id: automation_id_9
  //       trigger_id: button_buttonpresstrigger_id_5
  //   disabled_by_default: false
  //   id: template__templatebutton_id_4
  template__templatebutton_id_4 = new template_::TemplateButton();
  App.register_button(template__templatebutton_id_4);
  template__templatebutton_id_4->set_name("Confirm 144 LEDs");
  template__templatebutton_id_4->set_object_id("confirm_144_leds");
  template__templatebutton_id_4->set_disabled_by_default(false);
  button_buttonpresstrigger_id_5 = new button::ButtonPressTrigger(template__templatebutton_id_4);
  automation_id_9 = new Automation<>(button_buttonpresstrigger_id_5);
  // button.template:
  //   platform: template
  //   name: Show Current LED Count
  //   on_press:
  //     - then:
  //         - script.execute:
  //             id: show_current_leds
  //           type_id: script_scriptexecuteaction_id_6
  //       automation_id: automation_id_10
  //       trigger_id: button_buttonpresstrigger_id_6
  //   disabled_by_default: false
  //   id: template__templatebutton_id_5
  template__templatebutton_id_5 = new template_::TemplateButton();
  App.register_button(template__templatebutton_id_5);
  template__templatebutton_id_5->set_name("Show Current LED Count");
  template__templatebutton_id_5->set_object_id("show_current_led_count");
  template__templatebutton_id_5->set_disabled_by_default(false);
  button_buttonpresstrigger_id_6 = new button::ButtonPressTrigger(template__templatebutton_id_5);
  automation_id_10 = new Automation<>(button_buttonpresstrigger_id_6);
  // script:
  //   - id: led_scan_detect
  //     mode: restart
  //     then:
  //       - lambda: !lambda |-
  //           ESP_LOGI("led_scan", "开始LED扫描检测...");
  //         type_id: lambdaaction_id_3
  //       - light.turn_off:
  //           id: led_strip
  //           state: false
  //         type_id: light_lightcontrolaction_id_3
  //       - delay: 500ms
  //         type_id: delayaction_id
  //       - repeat:
  //           count: 300
  //           then:
  //             - lambda: !lambda |-
  //                 static int current_led = 0;
  //                 current_led++;
  //                 if(current_led > 300) current_led = 1;
  //                 ESP_LOGI("led_scan", "测试LED: %d", current_led);
  //                 id(led_count).publish_state(current_led);
  //               type_id: lambdaaction_id_4
  //             - light.addressable_set:
  //                 id: led_strip
  //                 range_from: 0
  //                 range_to: 299
  //                 red: 0.0
  //                 green: 0.0
  //                 blue: 0.0
  //               type_id: light_addressableset_id
  //             - light.addressable_set:
  //                 id: led_strip
  //                 range_from: !lambda |-
  //                   return (int)id(led_count).state - 1;
  //                 range_to: !lambda |-
  //                   return (int)id(led_count).state - 1;
  //                 red: 1.0
  //                 green: 0.0
  //                 blue: 0.0
  //               type_id: light_addressableset_id_2
  //             - delay: 200ms
  //               type_id: delayaction_id_2
  //         type_id: repeataction_id
  //     trigger_id: trigger_id_5
  //     automation_id: automation_id_11
  //     parameters: {}
  //   - id: show_current_leds
  //     then:
  //       - lambda: !lambda |-
  //           int current_count = (int)id(led_count).state;
  //           ESP_LOGI("led_show", "显示当前LED数量: %d", current_count);
  //         type_id: lambdaaction_id_5
  //       - light.addressable_set:
  //           id: led_strip
  //           range_from: 0
  //           range_to: 299
  //           red: 0.0
  //           green: 0.0
  //           blue: 0.0
  //         type_id: light_addressableset_id_3
  //       - light.addressable_set:
  //           id: led_strip
  //           range_from: 0
  //           range_to: !lambda |-
  //             return (int)id(led_count).state - 1;
  //           red: 0.0
  //           green: 0.0
  //           blue: 1.0
  //         type_id: light_addressableset_id_4
  //       - delay: 3s
  //         type_id: delayaction_id_3
  //       - light.addressable_set:
  //           id: led_strip
  //           range_from: 0
  //           range_to: 299
  //           red: 0.0
  //           green: 0.0
  //           blue: 0.0
  //         type_id: light_addressableset_id_5
  //     trigger_id: trigger_id_6
  //     automation_id: automation_id_12
  //     mode: single
  //     parameters: {}
  //   - id: confirm_led_count
  //     parameters:
  //       count: int
  //     then:
  //       - lambda: !lambda |-
  //           ESP_LOGI("led_confirm", "确认LED数量: %d", count);
  //           id(led_count).publish_state(count);
  //         type_id: lambdaaction_id_6
  //       - repeat:
  //           count: 3
  //           then:
  //             - light.addressable_set:
  //                 id: led_strip
  //                 range_from: 0
  //                 range_to: !lambda |-
  //                   return count - 1;
  //                 red: 0.0
  //                 green: 1.0
  //                 blue: 0.0
  //               type_id: light_addressableset_id_6
  //             - delay: 300ms
  //               type_id: delayaction_id_4
  //             - light.addressable_set:
  //                 id: led_strip
  //                 range_from: 0
  //                 range_to: 299
  //                 red: 0.0
  //                 green: 0.0
  //                 blue: 0.0
  //               type_id: light_addressableset_id_7
  //             - delay: 300ms
  //               type_id: delayaction_id_5
  //         type_id: repeataction_id_2
  //     trigger_id: trigger_id_7
  //     automation_id: automation_id_13
  //     mode: single
  led_scan_detect = new script::RestartScript<>();
  led_scan_detect->set_name("led_scan_detect");
  show_current_leds = new script::SingleScript<>();
  show_current_leds->set_name("show_current_leds");
  confirm_led_count = new script::SingleScript<int>();
  confirm_led_count->set_name("confirm_led_count");
  automation_id_11 = new Automation<>(led_scan_detect);
  lambdaaction_id_3 = new LambdaAction<>([=]() -> void {
      #line 148 "led-esp32c3.yaml"
      ESP_LOGI("led_scan", "开始LED扫描检测...");
  });
  // sensor.wifi_signal:
  //   platform: wifi_signal
  //   name: WiFi Signal
  //   update_interval: 60s
  //   disabled_by_default: false
  //   force_update: false
  //   id: wifi_signal_wifisignalsensor_id
  //   unit_of_measurement: dBm
  //   accuracy_decimals: 0
  //   device_class: signal_strength
  //   state_class: measurement
  //   entity_category: diagnostic
  wifi_signal_wifisignalsensor_id = new wifi_signal::WiFiSignalSensor();
  App.register_sensor(wifi_signal_wifisignalsensor_id);
  wifi_signal_wifisignalsensor_id->set_name("WiFi Signal");
  wifi_signal_wifisignalsensor_id->set_object_id("wifi_signal");
  wifi_signal_wifisignalsensor_id->set_disabled_by_default(false);
  wifi_signal_wifisignalsensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_signal_wifisignalsensor_id->set_device_class("signal_strength");
  wifi_signal_wifisignalsensor_id->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  wifi_signal_wifisignalsensor_id->set_unit_of_measurement("dBm");
  wifi_signal_wifisignalsensor_id->set_accuracy_decimals(0);
  wifi_signal_wifisignalsensor_id->set_force_update(false);
  wifi_signal_wifisignalsensor_id->set_update_interval(60000);
  wifi_signal_wifisignalsensor_id->set_component_source("wifi_signal.sensor");
  App.register_component(wifi_signal_wifisignalsensor_id);
  // sensor.uptime:
  //   platform: uptime
  //   name: Uptime
  //   update_interval: 60s
  //   disabled_by_default: false
  //   force_update: false
  //   id: uptime_uptimesecondssensor_id
  //   unit_of_measurement: s
  //   icon: mdi:timer-outline
  //   accuracy_decimals: 0
  //   device_class: duration
  //   state_class: total_increasing
  //   entity_category: diagnostic
  //   type: seconds
  uptime_uptimesecondssensor_id = new uptime::UptimeSecondsSensor();
  App.register_sensor(uptime_uptimesecondssensor_id);
  uptime_uptimesecondssensor_id->set_name("Uptime");
  uptime_uptimesecondssensor_id->set_object_id("uptime");
  uptime_uptimesecondssensor_id->set_disabled_by_default(false);
  uptime_uptimesecondssensor_id->set_icon("mdi:timer-outline");
  uptime_uptimesecondssensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  uptime_uptimesecondssensor_id->set_device_class("duration");
  uptime_uptimesecondssensor_id->set_state_class(sensor::STATE_CLASS_TOTAL_INCREASING);
  uptime_uptimesecondssensor_id->set_unit_of_measurement("s");
  uptime_uptimesecondssensor_id->set_accuracy_decimals(0);
  uptime_uptimesecondssensor_id->set_force_update(false);
  uptime_uptimesecondssensor_id->set_update_interval(60000);
  uptime_uptimesecondssensor_id->set_component_source("uptime.sensor");
  App.register_component(uptime_uptimesecondssensor_id);
  // text_sensor.wifi_info:
  //   platform: wifi_info
  //   ip_address:
  //     name: IP Address
  //     disabled_by_default: false
  //     id: wifi_info_ipaddresswifiinfo_id
  //     entity_category: diagnostic
  //     update_interval: 1s
  //   ssid:
  //     name: Connected SSID
  //     disabled_by_default: false
  //     id: wifi_info_ssidwifiinfo_id
  //     entity_category: diagnostic
  //     update_interval: 1s
  //   mac_address:
  //     name: Mac Address
  //     disabled_by_default: false
  //     id: wifi_info_macaddresswifiinfo_id
  //     entity_category: diagnostic
  wifi_info_ssidwifiinfo_id = new wifi_info::SSIDWiFiInfo();
  App.register_text_sensor(wifi_info_ssidwifiinfo_id);
  wifi_info_ssidwifiinfo_id->set_name("Connected SSID");
  wifi_info_ssidwifiinfo_id->set_object_id("connected_ssid");
  wifi_info_ssidwifiinfo_id->set_disabled_by_default(false);
  wifi_info_ssidwifiinfo_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_info_ssidwifiinfo_id->set_update_interval(1000);
  wifi_info_ssidwifiinfo_id->set_component_source("wifi_info.text_sensor");
  App.register_component(wifi_info_ssidwifiinfo_id);
  wifi_info_macaddresswifiinfo_id = new wifi_info::MacAddressWifiInfo();
  App.register_text_sensor(wifi_info_macaddresswifiinfo_id);
  wifi_info_macaddresswifiinfo_id->set_name("Mac Address");
  wifi_info_macaddresswifiinfo_id->set_object_id("mac_address");
  wifi_info_macaddresswifiinfo_id->set_disabled_by_default(false);
  wifi_info_macaddresswifiinfo_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_info_macaddresswifiinfo_id->set_component_source("wifi_info.text_sensor");
  App.register_component(wifi_info_macaddresswifiinfo_id);
  wifi_info_ipaddresswifiinfo_id = new wifi_info::IPAddressWiFiInfo();
  App.register_text_sensor(wifi_info_ipaddresswifiinfo_id);
  wifi_info_ipaddresswifiinfo_id->set_name("IP Address");
  wifi_info_ipaddresswifiinfo_id->set_object_id("ip_address");
  wifi_info_ipaddresswifiinfo_id->set_disabled_by_default(false);
  wifi_info_ipaddresswifiinfo_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_info_ipaddresswifiinfo_id->set_update_interval(1000);
  wifi_info_ipaddresswifiinfo_id->set_component_source("wifi_info.text_sensor");
  App.register_component(wifi_info_ipaddresswifiinfo_id);
  // number.template:
  //   platform: template
  //   name: LED Count
  //   id: led_count
  //   optimistic: true
  //   min_value: 1.0
  //   max_value: 300.0
  //   initial_value: 30.0
  //   step: 1.0
  //   mode: BOX
  //   on_value:
  //     - then:
  //         - lambda: !lambda |-
  //              更新LED数量设置（仅用于记录，实际LED数量由配置决定）
  //             ESP_LOGI("led_count", "LED数量设置为: %d", (int)x);
  //              关闭LED灯带以应用新设置
  //             auto call = id(led_strip).make_call();
  //             call.set_state(false);
  //             call.perform();
  //           type_id: lambdaaction_id_7
  //       automation_id: automation_id_14
  //       trigger_id: number_numberstatetrigger_id
  //   disabled_by_default: false
  //   update_interval: 60s
  led_count = new template_::TemplateNumber();
  led_count->set_update_interval(60000);
  led_count->set_component_source("template.number");
  App.register_component(led_count);
  App.register_number(led_count);
  led_count->set_name("LED Count");
  led_count->set_object_id("led_count");
  led_count->set_disabled_by_default(false);
  led_count->traits.set_min_value(1.0f);
  led_count->traits.set_max_value(300.0f);
  led_count->traits.set_step(1.0f);
  led_count->traits.set_mode(number::NUMBER_MODE_BOX);
  number_numberstatetrigger_id = new number::NumberStateTrigger(led_count);
  automation_id_14 = new Automation<float>(number_numberstatetrigger_id);
  // light.fastled_clockless:
  //   platform: fastled_clockless
  //   chipset: WS2812B
  //   pin: 18
  //   num_leds: 300
  //   rgb_order: GRB
  //   name: WS2812 LED Strip
  //   id: led_strip
  //   effects:
  //     - addressable_rainbow:
  //         name: Rainbow
  //         speed: 10
  //         width: 50
  //       type_id: light_addressablerainbowlighteffect_id
  //     - addressable_color_wipe:
  //         name: Color Wipe
  //         colors:
  //           - red: 1.0
  //             green: 1.0
  //             blue: 1.0
  //             num_leds: 1
  //             white: 1.0
  //             random: false
  //             gradient: false
  //           - red: 0.0
  //             green: 0.0
  //             blue: 0.0
  //             num_leds: 1
  //             white: 1.0
  //             random: false
  //             gradient: false
  //         add_led_interval: 100ms
  //         reverse: false
  //       type_id: light_addressablecolorwipeeffect_id
  //     - addressable_scan:
  //         name: Scan
  //         move_interval: 100ms
  //         scan_width: 1
  //       type_id: light_addressablescaneffect_id
  //     - addressable_twinkle:
  //         name: Twinkle
  //         twinkle_probability: 0.05
  //         progress_interval: 4ms
  //       type_id: light_addressabletwinkleeffect_id
  //     - addressable_random_twinkle:
  //         name: Random Twinkle
  //         twinkle_probability: 0.05
  //         progress_interval: 32ms
  //       type_id: light_addressablerandomtwinkleeffect_id
  //     - addressable_fireworks:
  //         name: Fireworks
  //         update_interval: 32ms
  //         spark_probability: 0.1
  //         use_random_color: false
  //         fade_out_rate: 120
  //       type_id: light_addressablefireworkseffect_id
  //     - addressable_flicker:
  //         name: Flicker
  //         update_interval: 16ms
  //         intensity: 0.05
  //       type_id: light_addressableflickereffect_id
  //   disabled_by_default: false
  //   restore_mode: ALWAYS_OFF
  //   gamma_correct: 2.8
  //   default_transition_length: 1s
  //   flash_transition_length: 0s
  //   output_id: fastled_base_fastledlightoutput_id
  fastled_base_fastledlightoutput_id = new fastled_base::FastLEDLightOutput();
  fastled_base_fastledlightoutput_id->set_component_source("fastled_base");
  App.register_component(fastled_base_fastledlightoutput_id);
  led_strip = new light::AddressableLightState(fastled_base_fastledlightoutput_id);
  App.register_light(led_strip);
  led_strip->set_component_source("light");
  App.register_component(led_strip);
  led_strip->set_name("WS2812 LED Strip");
  led_strip->set_object_id("ws2812_led_strip");
  led_strip->set_disabled_by_default(false);
  led_strip->set_restore_mode(light::LIGHT_ALWAYS_OFF);
  led_strip->set_default_transition_length(1000);
  led_strip->set_flash_transition_length(0);
  led_strip->set_gamma_correct(2.8f);
  light_addressablerainbowlighteffect_id = new light::AddressableRainbowLightEffect("Rainbow");
  light_addressablerainbowlighteffect_id->set_speed(10);
  light_addressablerainbowlighteffect_id->set_width(50);
  light_addressablecolorwipeeffect_id = new light::AddressableColorWipeEffect("Color Wipe");
  light_addressablecolorwipeeffect_id->set_add_led_interval(100);
  light_addressablecolorwipeeffect_id->set_reverse(false);
  light_addressablecolorwipeeffect_id->set_colors({light::AddressableColorWipeEffectColor{
      .r = 255,
      .g = 255,
      .b = 255,
      .w = 255,
      .random = false,
      .num_leds = 1,
      .gradient = false,
    }, light::AddressableColorWipeEffectColor{
      .r = 0,
      .g = 0,
      .b = 0,
      .w = 255,
      .random = false,
      .num_leds = 1,
      .gradient = false,
  }});
  light_addressablescaneffect_id = new light::AddressableScanEffect("Scan");
  light_addressablescaneffect_id->set_move_interval(100);
  light_addressablescaneffect_id->set_scan_width(1);
  light_addressabletwinkleeffect_id = new light::AddressableTwinkleEffect("Twinkle");
  light_addressabletwinkleeffect_id->set_twinkle_probability(0.05f);
  light_addressabletwinkleeffect_id->set_progress_interval(4);
  light_addressablerandomtwinkleeffect_id = new light::AddressableRandomTwinkleEffect("Random Twinkle");
  light_addressablerandomtwinkleeffect_id->set_twinkle_probability(0.05f);
  light_addressablerandomtwinkleeffect_id->set_progress_interval(32);
  light_addressablefireworkseffect_id = new light::AddressableFireworksEffect("Fireworks");
  light_addressablefireworkseffect_id->set_update_interval(32);
  light_addressablefireworkseffect_id->set_spark_probability(0.1f);
  light_addressablefireworkseffect_id->set_use_random_color(false);
  light_addressablefireworkseffect_id->set_fade_out_rate(120);
  light_addressableflickereffect_id = new light::AddressableFlickerEffect("Flicker");
  light_addressableflickereffect_id->set_update_interval(16);
  light_addressableflickereffect_id->set_intensity(0.05f);
  led_strip->add_effects({light_addressablerainbowlighteffect_id, light_addressablecolorwipeeffect_id, light_addressablescaneffect_id, light_addressabletwinkleeffect_id, light_addressablerandomtwinkleeffect_id, light_addressablefireworkseffect_id, light_addressableflickereffect_id});
  fastled_base_fastledlightoutput_id->add_leds<WS2812B, 18, GRB>(300);
  // md5:
  // socket:
  //   implementation: bsd_sockets
  light_lightcontrolaction_id_2 = new light::LightControlAction<>(led_strip);
  light_lightcontrolaction_id_2->set_state(false);
  automation_id_2->add_actions({light_lightcontrolaction_id_2});
  automation_id = new Automation<>(led_power->get_turn_on_trigger());
  light_lightcontrolaction_id = new light::LightControlAction<>(led_strip);
  light_lightcontrolaction_id->set_state(true);
  automation_id->add_actions({light_lightcontrolaction_id});
  led_power->set_optimistic(true);
  led_power->set_assumed_state(false);
  lambdaaction_id = new LambdaAction<>([=]() -> void {
      #line 39 "led-esp32c3.yaml"
       
      ESP_LOGI("led_detect", "开始自动检测LED数量...");
      
       
      auto call = led_strip->make_call();
      call.set_state(false);
      call.perform();
      
       
      int detected_count = 30;  
      
       
       
      detected_count = (int)led_count->state;
      
      ESP_LOGI("led_detect", "检测到LED数量: %d", detected_count);
      
       
      led_count->publish_state(detected_count);
  });
  automation_id_3->add_actions({lambdaaction_id});
  auto_detect_leds->set_optimistic(false);
  auto_detect_leds->set_assumed_state(false);
  script_scriptexecuteaction_id = new script::ScriptExecuteAction<script::Script<>>(led_scan_detect);
  script_scriptexecuteaction_id->set_args();
  automation_id_5->add_actions({script_scriptexecuteaction_id});
  script_scriptexecuteaction_id_2 = new script::ScriptExecuteAction<script::Script<int>>(confirm_led_count);
  script_scriptexecuteaction_id_2->set_args(10);
  automation_id_6->add_actions({script_scriptexecuteaction_id_2});
  script_scriptexecuteaction_id_3 = new script::ScriptExecuteAction<script::Script<int>>(confirm_led_count);
  script_scriptexecuteaction_id_3->set_args(30);
  automation_id_7->add_actions({script_scriptexecuteaction_id_3});
  script_scriptexecuteaction_id_4 = new script::ScriptExecuteAction<script::Script<int>>(confirm_led_count);
  script_scriptexecuteaction_id_4->set_args(60);
  automation_id_8->add_actions({script_scriptexecuteaction_id_4});
  script_scriptexecuteaction_id_5 = new script::ScriptExecuteAction<script::Script<int>>(confirm_led_count);
  script_scriptexecuteaction_id_5->set_args(144);
  automation_id_9->add_actions({script_scriptexecuteaction_id_5});
  script_scriptexecuteaction_id_6 = new script::ScriptExecuteAction<script::Script<>>(show_current_leds);
  script_scriptexecuteaction_id_6->set_args();
  automation_id_10->add_actions({script_scriptexecuteaction_id_6});
  light_lightcontrolaction_id_3 = new light::LightControlAction<>(led_strip);
  light_lightcontrolaction_id_3->set_state(false);
  delayaction_id = new DelayAction<>();
  delayaction_id->set_component_source("script");
  App.register_component(delayaction_id);
  delayaction_id->set_delay(500);
  repeataction_id = new RepeatAction<>();
  repeataction_id->set_count(300);
  lambdaaction_id_4 = new LambdaAction<uint32_t>([=](uint32_t iteration) -> void {
      #line 157 "led-esp32c3.yaml"
      static int current_led = 0;
      current_led++;
      if(current_led > 300) current_led = 1;
      ESP_LOGI("led_scan", "测试LED: %d", current_led);
      led_count->publish_state(current_led);
  });
  light_addressableset_id = new light::AddressableSet<uint32_t>(led_strip);
  light_addressableset_id->set_range_from(0);
  light_addressableset_id->set_range_to(299);
  light_addressableset_id->set_red(0.0f);
  light_addressableset_id->set_green(0.0f);
  light_addressableset_id->set_blue(0.0f);
  light_addressableset_id_2 = new light::AddressableSet<uint32_t>(led_strip);
  light_addressableset_id_2->set_range_from([=](uint32_t iteration) -> int32_t {
      #line 173 "led-esp32c3.yaml"
      return (int)led_count->state - 1;
  });
  light_addressableset_id_2->set_range_to([=](uint32_t iteration) -> int32_t {
      #line 174 "led-esp32c3.yaml"
      return (int)led_count->state - 1;
  });
  light_addressableset_id_2->set_red(1.0f);
  light_addressableset_id_2->set_green(0.0f);
  light_addressableset_id_2->set_blue(0.0f);
  delayaction_id_2 = new DelayAction<uint32_t>();
  delayaction_id_2->set_component_source("script");
  App.register_component(delayaction_id_2);
  delayaction_id_2->set_delay(200);
  repeataction_id->add_then({lambdaaction_id_4, light_addressableset_id, light_addressableset_id_2, delayaction_id_2});
  automation_id_11->add_actions({lambdaaction_id_3, light_lightcontrolaction_id_3, delayaction_id, repeataction_id});
  automation_id_12 = new Automation<>(show_current_leds);
  lambdaaction_id_5 = new LambdaAction<>([=]() -> void {
      #line 184 "led-esp32c3.yaml"
      int current_count = (int)led_count->state;
      ESP_LOGI("led_show", "显示当前LED数量: %d", current_count);
  });
  light_addressableset_id_3 = new light::AddressableSet<>(led_strip);
  light_addressableset_id_3->set_range_from(0);
  light_addressableset_id_3->set_range_to(299);
  light_addressableset_id_3->set_red(0.0f);
  light_addressableset_id_3->set_green(0.0f);
  light_addressableset_id_3->set_blue(0.0f);
  light_addressableset_id_4 = new light::AddressableSet<>(led_strip);
  light_addressableset_id_4->set_range_from(0);
  light_addressableset_id_4->set_range_to([=]() -> int32_t {
      #line 198 "led-esp32c3.yaml"
      return (int)led_count->state - 1;
  });
  light_addressableset_id_4->set_red(0.0f);
  light_addressableset_id_4->set_green(0.0f);
  light_addressableset_id_4->set_blue(1.0f);
  delayaction_id_3 = new DelayAction<>();
  delayaction_id_3->set_component_source("script");
  App.register_component(delayaction_id_3);
  delayaction_id_3->set_delay(3000);
  light_addressableset_id_5 = new light::AddressableSet<>(led_strip);
  light_addressableset_id_5->set_range_from(0);
  light_addressableset_id_5->set_range_to(299);
  light_addressableset_id_5->set_red(0.0f);
  light_addressableset_id_5->set_green(0.0f);
  light_addressableset_id_5->set_blue(0.0f);
  automation_id_12->add_actions({lambdaaction_id_5, light_addressableset_id_3, light_addressableset_id_4, delayaction_id_3, light_addressableset_id_5});
  automation_id_13 = new Automation<int>(confirm_led_count);
  lambdaaction_id_6 = new LambdaAction<int>([=](int count) -> void {
      #line 218 "led-esp32c3.yaml"
      ESP_LOGI("led_confirm", "确认LED数量: %d", count);
      led_count->publish_state(count);
  });
  repeataction_id_2 = new RepeatAction<int>();
  repeataction_id_2->set_count(3);
  light_addressableset_id_6 = new light::AddressableSet<uint32_t, int>(led_strip);
  light_addressableset_id_6->set_range_from(0);
  light_addressableset_id_6->set_range_to([=](uint32_t iteration, int count) -> int32_t {
      #line 228 "led-esp32c3.yaml"
      return count - 1;
  });
  light_addressableset_id_6->set_red(0.0f);
  light_addressableset_id_6->set_green(1.0f);
  light_addressableset_id_6->set_blue(0.0f);
  delayaction_id_4 = new DelayAction<uint32_t, int>();
  delayaction_id_4->set_component_source("script");
  App.register_component(delayaction_id_4);
  delayaction_id_4->set_delay(300);
  light_addressableset_id_7 = new light::AddressableSet<uint32_t, int>(led_strip);
  light_addressableset_id_7->set_range_from(0);
  light_addressableset_id_7->set_range_to(299);
  light_addressableset_id_7->set_red(0.0f);
  light_addressableset_id_7->set_green(0.0f);
  light_addressableset_id_7->set_blue(0.0f);
  delayaction_id_5 = new DelayAction<uint32_t, int>();
  delayaction_id_5->set_component_source("script");
  App.register_component(delayaction_id_5);
  delayaction_id_5->set_delay(300);
  repeataction_id_2->add_then({light_addressableset_id_6, delayaction_id_4, light_addressableset_id_7, delayaction_id_5});
  automation_id_13->add_actions({lambdaaction_id_6, repeataction_id_2});
  lambdaaction_id_7 = new LambdaAction<float>([=](float x) -> void {
      #line 280 "led-esp32c3.yaml"
       
      ESP_LOGI("led_count", "LED数量设置为: %d", (int)x);
       
      auto call = led_strip->make_call();
      call.set_state(false);
      call.perform();
  });
  automation_id_14->add_actions({lambdaaction_id_7});
  led_count->set_optimistic(true);
  led_count->set_initial_value(30.0f);
  // =========== AUTO GENERATED CODE END ============
  App.setup();
}

void loop() {
  App.loop();
}
