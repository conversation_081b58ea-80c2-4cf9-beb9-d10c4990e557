{"storage_version": 1, "name": "led-myesp32c3", "friendly_name": "Fastled ESP32-C3", "comment": null, "esphome_version": "2025.7.2", "src_version": 1, "address": "led-myesp32c3.local", "web_port": 80, "esp_platform": "ESP32C3", "build_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\led-myesp32c3", "firmware_bin_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\led-myesp32c3\\.pioenvs\\led-myesp32c3\\firmware.bin", "loaded_integrations": ["api", "async_tcp", "binary_sensor", "button", "captive_portal", "esp32", "esphome", "fastled_base", "fastled_clockless", "json", "light", "logger", "md5", "mdns", "network", "number", "ota", "preferences", "restart", "safe_mode", "script", "sensor", "socket", "status", "switch", "template", "text_sensor", "uptime", "web_server", "web_server_base", "wifi", "wifi_info", "wifi_signal"], "loaded_platforms": ["binary_sensor/status", "button/template", "light/fastled_clockless", "number/template", "ota/esphome", "ota/web_server", "sensor/uptime", "sensor/wifi_signal", "switch/restart", "switch/template", "text_sensor/wifi_info"], "no_mdns": false, "framework": "a<PERSON><PERSON><PERSON>", "core_platform": "esp32"}