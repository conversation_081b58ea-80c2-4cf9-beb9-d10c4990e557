{"storage_version": 1, "name": "myesp32c3-sht30", "friendly_name": "SHT30 ESP32-C3 Sensor", "comment": null, "esphome_version": "2025.7.2", "src_version": 1, "address": "myesp32c3-sht30.local", "web_port": 80, "esp_platform": "ESP32C3", "build_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp32c3-sht30", "firmware_bin_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp32c3-sht30\\.pioenvs\\myesp32c3-sht30\\firmware.bin", "loaded_integrations": ["api", "async_tcp", "binary_sensor", "esp32", "esphome", "globals", "internal_temperature", "interval", "json", "logger", "md5", "mdns", "network", "ota", "preferences", "restart", "safe_mode", "sensor", "socket", "status", "switch", "template", "text_sensor", "uart", "uptime", "web_server", "web_server_base", "wifi", "wifi_info", "wifi_signal"], "loaded_platforms": ["binary_sensor/status", "ota/esphome", "sensor/internal_temperature", "sensor/template", "sensor/uptime", "sensor/wifi_signal", "switch/restart", "text_sensor/wifi_info"], "no_mdns": false, "framework": "a<PERSON><PERSON><PERSON>", "core_platform": "esp32"}