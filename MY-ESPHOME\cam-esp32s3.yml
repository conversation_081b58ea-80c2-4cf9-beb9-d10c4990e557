esphome:
  name: esp32s3_cam
  platformio_options:
    monitor_speed: 115200
    board_build.arduino.partitions: default_16MB.csv
    board_build.arduino.memory_type: qio_opi

esp32:
  board: esp32-s3-devkitc-1
  framework:
    type: arduino
  flash_size: 16MB

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

# 可选：开启日志与 OTA
logger:
api:
ota:
  platform: esphome

# 摄像头配置（以 DFRobot ESP32-S3 AI CAM 为例，具体针脚请参考你的模块文档）
esp32_camera:
  name: "客厅摄像头"
  external_clock:
    pin: GPIO5
    frequency: 20MHz
  i2c_pins:
    sda: GPIO8
    scl: GPIO9
  data_pins: [GPIO16, GPIO18, GPIO21, GPIO17, GPIO14, GPIO7, GPIO6, GPIO4]
  vsync_pin: GPIO1
  href_pin: GPIO2
  pixel_clock_pin: GPIO15
  power_down_pin: GPIO47        # DFRobot 模块上红外灯控制
  resolution: 640x480
  jpeg_quality: 10

# 可选：内置 HTTP 流媒体
esp32_camera_web_server:
  - port: 8080
    mode: stream
  - port: 8081
    mode: snapshot


# Web服务器
web_server:
  port: 80
  auth:
    username: admin
    password: !secret wifi_password
