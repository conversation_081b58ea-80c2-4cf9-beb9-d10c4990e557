esphome:
  name: ws2812-auto
  friendly_name: "WS2812 Auto LED Strip"

esp32:
  board: airm2m_core_esp32c3
  framework:
    type: arduino

# WiFi配置
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  
  # 启用回退热点
  ap:
    ssid: "WS2812-Auto Fallback"
    password: "12345678"

captive_portal:

# API配置
api:

# OTA更新
ota:
  platform: esphome

# Web服务器
web_server:
  port: 80

# 日志配置
logger:
  level: INFO

# 数字输入 - 动态设置LED数量
number:
  - platform: template
    name: "LED Count"
    id: led_count
    optimistic: true
    min_value: 1
    max_value: 300
    initial_value: 30
    step: 1
    mode: box
    on_value:
      then:
        - lambda: |-
            ESP_LOGI("led_config", "设置LED数量为: %d", (int)x);
            // 更新LED数量后重新初始化
            id(led_strip).set_num_leds((int)x);

# 按钮控制
button:
  - platform: template
    name: "扫描检测LED数量"
    id: start_led_scan
    on_press:
      - script.execute: led_scan_detect
  
  - platform: template
    name: "确认10个LED"
    on_press:
      - script.execute:
          id: confirm_led_count
          count: 10
  
  - platform: template
    name: "确认30个LED"
    on_press:
      - script.execute:
          id: confirm_led_count
          count: 30
          
  - platform: template
    name: "确认60个LED"
    on_press:
      - script.execute:
          id: confirm_led_count
          count: 60
          
  - platform: template
    name: "确认144个LED"
    on_press:
      - script.execute:
          id: confirm_led_count
          count: 144

  - platform: template
    name: "测试单个LED"
    on_press:
      - script.execute: test_single_led

# 脚本
script:
  # LED扫描检测脚本
  - id: led_scan_detect
    mode: restart
    then:
      - lambda: |-
          ESP_LOGI("led_scan", "开始LED扫描检测...");
          
          // 先关闭所有LED
          auto call = id(led_strip).make_call();
          call.set_state(false);
          call.perform();
          delay(1000);
          
          // 逐个点亮LED进行测试 (每10个一组)
          for(int i = 10; i <= 300; i += 10) {
            ESP_LOGI("led_scan", "测试 %d 个LED", i);
            
            // 设置LED数量
            id(led_strip).set_num_leds(i);
            
            // 点亮最后一个LED (红色)
            auto call = id(led_strip).make_call();
            call.set_state(true);
            call.set_brightness(0.5);
            call.set_rgb(1.0, 0.0, 0.0);
            call.perform();
            
            delay(500); // 等待500ms观察
            
            // 关闭LED
            call.set_state(false);
            call.perform();
            delay(200);
          }
          
          ESP_LOGI("led_scan", "扫描完成，请使用确认按钮设置正确的LED数量");
      
  # LED计数确认脚本  
  - id: confirm_led_count
    parameters:
      count: int
    then:
      - lambda: |-
          ESP_LOGI("led_confirm", "确认LED数量: %d", count);
          id(led_count).publish_state(count);
          id(led_strip).set_num_leds(count);
          
      # 显示确认效果 - 彩虹效果
      - light.turn_on:
          id: led_strip
          effect: "Rainbow"
          brightness: 80%
      - delay: 3s
      - light.turn_off: led_strip

  # 测试单个LED
  - id: test_single_led
    mode: restart
    then:
      - lambda: |-
          int total_leds = (int)id(led_count).state;
          ESP_LOGI("led_test", "测试 %d 个LED，逐个点亮", total_leds);
          
          // 确保LED数量正确
          id(led_strip).set_num_leds(total_leds);
          
          // 先关闭所有LED
          auto call = id(led_strip).make_call();
          call.set_state(false);
          call.perform();
          delay(500);
          
          // 逐个点亮每个LED
          for(int i = 0; i < total_leds; i++) {
            ESP_LOGI("led_test", "点亮第 %d 个LED", i+1);
            
            auto call = id(led_strip).make_call();
            call.set_state(true);
            call.set_brightness(0.3);
            
            // 计算颜色 (彩虹色)
            float hue = (float)i / total_leds * 360.0;
            float r, g, b;
            
            // 简单的HSV到RGB转换
            if(hue < 120) {
              r = (120 - hue) / 120.0;
              g = hue / 120.0;
              b = 0;
            } else if(hue < 240) {
              r = 0;
              g = (240 - hue) / 120.0;
              b = (hue - 120) / 120.0;
            } else {
              r = (hue - 240) / 120.0;
              g = 0;
              b = (360 - hue) / 120.0;
            }
            
            call.set_rgb(r, g, b);
            call.perform();
            
            delay(200); // 每个LED显示200ms
          }
          
          delay(1000);
          
          // 关闭所有LED
          call.set_state(false);
          call.perform();

# WS2812 LED灯带配置
light:
  - platform: fastled_clockless
    chipset: WS2812B
    pin: GPIO8  # 数据引脚
    num_leds: 30  # 初始LED数量
    rgb_order: GRB
    name: "WS2812 LED Strip"
    id: led_strip
    effects:
      - addressable_rainbow:
          name: Rainbow
          speed: 10
          width: 50
      - addressable_color_wipe:
          name: Color Wipe
      - addressable_scan:
          name: Scan
      - addressable_twinkle:
          name: Twinkle
      - addressable_fireworks:
          name: Fireworks

# 传感器
sensor:
  - platform: wifi_signal
    name: "WiFi Signal"
    update_interval: 60s
  
  - platform: uptime
    name: "Uptime"
    update_interval: 60s

# 开关
switch:
  - platform: restart
    name: "Restart"
