// Auto generated code by esphome
// ========== AUTO GENERATED INCLUDE BLOCK BEGIN ===========
#include "esphome.h"
using namespace esphome;
using std::isnan;
using std::min;
using std::max;
using namespace sensor;
using namespace text_sensor;
using namespace binary_sensor;
using namespace switch_;
logger::Logger *logger_logger_id;
web_server_base::WebServerBase *web_server_base_webserverbase_id;
wifi::WiFiComponent *wifi_wificomponent_id;
mdns::MDNSComponent *mdns_mdnscomponent_id;
esphome::ESPHomeOTAComponent *esphome_esphomeotacomponent_id;
safe_mode::SafeModeComponent *safe_mode_safemodecomponent_id;
Automation<> *automation_id;
LambdaAction<> *lambdaaction_id;
Automation<> *automation_id_2;
LambdaAction<> *lambdaaction_id_2;
api::APIServer *api_apiserver_id;
using namespace api;
web_server::WebServer *web_server_webserver_id;
const uint8_t ESPHOME_WEBSERVER_INDEX_H<PERSON>L[174] PROGMEM = {60, 33, 68, 79, 67, 84, 89, 80, 69, 32, 104, 116, 109, 108, 62, 60, 104, 116, 109, 108, 62, 60, 104, 101, 97, 100, 62, 60, 109, 101, 116, 97, 32, 99, 104, 97, 114, 115, 101, 116, 61, 85, 84, 70, 45, 56, 62, 60, 108, 105, 110, 107, 32, 114, 101, 108, 61, 105, 99, 111, 110, 32, 104, 114, 101, 102, 61, 100, 97, 116, 97, 58, 62, 60, 47, 104, 101, 97, 100, 62, 60, 98, 111, 100, 121, 62, 60, 101, 115, 112, 45, 97, 112, 112, 62, 60, 47, 101, 115, 112, 45, 97, 112, 112, 62, 60, 115, 99, 114, 105, 112, 116, 32, 115, 114, 99, 61, 34, 104, 116, 116, 112, 115, 58, 47, 47, 111, 105, 46, 101, 115, 112, 104, 111, 109, 101, 46, 105, 111, 47, 118, 50, 47, 119, 119, 119, 46, 106, 115, 34, 62, 60, 47, 115, 99, 114, 105, 112, 116, 62, 60, 47, 98, 111, 100, 121, 62, 60, 47, 104, 116, 109, 108, 62};
const size_t ESPHOME_WEBSERVER_INDEX_HTML_SIZE = 174;
using namespace json;
preferences::IntervalSyncer *preferences_intervalsyncer_id;
using namespace uart;
uart::ESP32ArduinoUARTComponent *uart_bus;
esp32::ESP32InternalGPIOPin *esp32_esp32internalgpiopin_id;
esp32::ESP32InternalGPIOPin *esp32_esp32internalgpiopin_id_2;
template_::TemplateSensor *voc_tvoc;
template_::TemplateSensor *voc_hcho;
template_::TemplateSensor *voc_co2;
internal_temperature::InternalTemperatureSensor *internal_temperature_internaltemperaturesensor_id;
interval::IntervalTrigger *interval_intervaltrigger_id;
Automation<> *automation_id_3;
wifi_info::MacAddressWifiInfo *wifi_info_macaddresswifiinfo_id;
status::StatusBinarySensor *status_statusbinarysensor_id;
restart::RestartSwitch *restart_restartswitch_id;
globals::GlobalsComponent<float> *last_tvoc;
globals::GlobalsComponent<float> *last_hcho;
globals::GlobalsComponent<float> *last_co2;
LambdaAction<> *lambdaaction_id_3;
#define yield() esphome::yield()
#define millis() esphome::millis()
#define micros() esphome::micros()
#define delay(x) esphome::delay(x)
#define delayMicroseconds(x) esphome::delayMicroseconds(x)
// ========== AUTO GENERATED INCLUDE BLOCK END ==========="

void setup() {
  // ========== AUTO GENERATED CODE BEGIN ===========
  App.reserve_text_sensor(1);
  App.reserve_switch(1);
  App.reserve_sensor(4);
  App.reserve_binary_sensor(1);
  // network:
  //   enable_ipv6: false
  //   min_ipv6_addr_count: 0
  // async_tcp:
  //   {}
  // esphome:
  //   name: voc-myesp32c3
  //   friendly_name: VOC-CO2-HCHO ESP32-C3 Sensor
  //   min_version: 2025.7.2
  //   build_path: build\voc-myesp32c3
  //   platformio_options: {}
  //   includes: []
  //   libraries: []
  //   name_add_mac_suffix: false
  //   debug_scheduler: false
  //   areas: []
  //   devices: []
  App.pre_setup("voc-myesp32c3", "VOC-CO2-HCHO ESP32-C3 Sensor", "", __DATE__ ", " __TIME__, false);
  App.reserve_components(21);
  // sensor:
  // text_sensor:
  // binary_sensor:
  // switch:
  // logger:
  //   baud_rate: 115200
  //   level: INFO
  //   logs:
  //     voc_uart: INFO
  //     uart_debug: WARN
  //     uart: WARN
  //     wifi: WARN
  //     api: WARN
  //     ota: WARN
  //     web_server: WARN
  //   id: logger_logger_id
  //   tx_buffer_size: 512
  //   deassert_rts_dtr: false
  //   task_log_buffer_size: 768
  //   hardware_uart: USB_CDC
  logger_logger_id = new logger::Logger(115200, 512);
  logger_logger_id->create_pthread_key();
  logger_logger_id->init_log_buffer(768);
  logger_logger_id->set_log_level(ESPHOME_LOG_LEVEL_INFO);
  logger_logger_id->set_uart_selection(logger::UART_SELECTION_USB_CDC);
  logger_logger_id->pre_setup();
  logger_logger_id->set_log_level("voc_uart", ESPHOME_LOG_LEVEL_INFO);
  logger_logger_id->set_log_level("uart_debug", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("uart", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("wifi", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("api", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("ota", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("web_server", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_component_source("logger");
  App.register_component(logger_logger_id);
  // web_server_base:
  //   id: web_server_base_webserverbase_id
  web_server_base_webserverbase_id = new web_server_base::WebServerBase();
  web_server_base_webserverbase_id->set_component_source("web_server_base");
  App.register_component(web_server_base_webserverbase_id);
  web_server_base::global_web_server_base = web_server_base_webserverbase_id;
  // wifi:
  //   ap:
  //     ssid: ESP32C3-VOC Fallback Hotspot
  //     password: '12345678'
  //     id: wifi_wifiap_id
  //     ap_timeout: 1min
  //   on_connect:
  //     then:
  //       - logger.log:
  //           format: WiFi连接成功！
  //           args: []
  //           tag: main
  //           level: DEBUG
  //         type_id: lambdaaction_id
  //     trigger_id: trigger_id
  //     automation_id: automation_id
  //   on_disconnect:
  //     then:
  //       - logger.log:
  //           format: WiFi连接断开！
  //           args: []
  //           tag: main
  //           level: DEBUG
  //         type_id: lambdaaction_id_2
  //     trigger_id: trigger_id_2
  //     automation_id: automation_id_2
  //   id: wifi_wificomponent_id
  //   domain: .local
  //   reboot_timeout: 15min
  //   power_save_mode: LIGHT
  //   fast_connect: false
  //   passive_scan: false
  //   enable_on_boot: true
  //   networks:
  //     - ssid: !secret 'wifi_ssid'
  //       password: !secret 'wifi_password'
  //       id: wifi_wifiap_id_2
  //       priority: 0.0
  //   use_address: voc-myesp32c3.local
  wifi_wificomponent_id = new wifi::WiFiComponent();
  wifi_wificomponent_id->set_use_address("voc-myesp32c3.local");
  {
  wifi::WiFiAP wifi_wifiap_id_2 = wifi::WiFiAP();
  wifi_wifiap_id_2.set_ssid("HOME");
  wifi_wifiap_id_2.set_password("nb9d30@24zd");
  wifi_wifiap_id_2.set_priority(0.0f);
  wifi_wificomponent_id->add_sta(wifi_wifiap_id_2);
  }
  {
  wifi::WiFiAP wifi_wifiap_id = wifi::WiFiAP();
  wifi_wifiap_id.set_ssid("ESP32C3-VOC Fallback Hotspot");
  wifi_wifiap_id.set_password("12345678");
  wifi_wificomponent_id->set_ap(wifi_wifiap_id);
  }
  wifi_wificomponent_id->set_ap_timeout(60000);
  wifi_wificomponent_id->set_reboot_timeout(900000);
  wifi_wificomponent_id->set_power_save_mode(wifi::WIFI_POWER_SAVE_LIGHT);
  wifi_wificomponent_id->set_fast_connect(false);
  wifi_wificomponent_id->set_passive_scan(false);
  wifi_wificomponent_id->set_enable_on_boot(true);
  wifi_wificomponent_id->set_component_source("wifi");
  App.register_component(wifi_wificomponent_id);
  // mdns:
  //   id: mdns_mdnscomponent_id
  //   disabled: false
  //   services: []
  mdns_mdnscomponent_id = new mdns::MDNSComponent();
  mdns_mdnscomponent_id->set_component_source("mdns");
  App.register_component(mdns_mdnscomponent_id);
  // ota:
  // ota.esphome:
  //   platform: esphome
  //   id: esphome_esphomeotacomponent_id
  //   version: 2
  //   port: 3232
  esphome_esphomeotacomponent_id = new esphome::ESPHomeOTAComponent();
  esphome_esphomeotacomponent_id->set_port(3232);
  esphome_esphomeotacomponent_id->set_component_source("esphome.ota");
  App.register_component(esphome_esphomeotacomponent_id);
  // safe_mode:
  //   id: safe_mode_safemodecomponent_id
  //   boot_is_good_after: 1min
  //   disabled: false
  //   num_attempts: 10
  //   reboot_timeout: 5min
  safe_mode_safemodecomponent_id = new safe_mode::SafeModeComponent();
  safe_mode_safemodecomponent_id->set_component_source("safe_mode");
  App.register_component(safe_mode_safemodecomponent_id);
  if (safe_mode_safemodecomponent_id->should_enter_safe_mode(10, 300000, 60000)) return;
  automation_id = new Automation<>(wifi_wificomponent_id->get_connect_trigger());
  lambdaaction_id = new LambdaAction<>([=]() -> void {
      ESP_LOGD("main", "WiFi\350\277\236\346\216\245\346\210\220\345\212\237\357\274\201");
  });
  automation_id->add_actions({lambdaaction_id});
  automation_id_2 = new Automation<>(wifi_wificomponent_id->get_disconnect_trigger());
  lambdaaction_id_2 = new LambdaAction<>([=]() -> void {
      ESP_LOGD("main", "WiFi\350\277\236\346\216\245\346\226\255\345\274\200\357\274\201");
  });
  automation_id_2->add_actions({lambdaaction_id_2});
  // api:
  //   id: api_apiserver_id
  //   port: 6053
  //   password: ''
  //   reboot_timeout: 15min
  //   batch_delay: 100ms
  //   custom_services: false
  api_apiserver_id = new api::APIServer();
  api_apiserver_id->set_component_source("api");
  App.register_component(api_apiserver_id);
  api_apiserver_id->set_port(6053);
  api_apiserver_id->set_reboot_timeout(900000);
  api_apiserver_id->set_batch_delay(100);
  // web_server:
  //   port: 80
  //   auth:
  //     username: admin
  //     password: !secret 'wifi_password'
  //   id: web_server_webserver_id
  //   version: 2
  //   enable_private_network_access: true
  //   web_server_base_id: web_server_base_webserverbase_id
  //   include_internal: false
  //   log: true
  //   css_url: ''
  //   js_url: https:oi.esphome.io/v2/www.js
  web_server_webserver_id = new web_server::WebServer(web_server_base_webserverbase_id);
  web_server_webserver_id->set_component_source("web_server");
  App.register_component(web_server_webserver_id);
  web_server_base_webserverbase_id->set_port(80);
  web_server_webserver_id->set_expose_log(true);
  web_server_base_webserverbase_id->set_auth_username("admin");
  web_server_base_webserverbase_id->set_auth_password("nb9d30@24zd");
  web_server_webserver_id->set_include_internal(false);
  // json:
  //   {}
  // esp32:
  //   board: airm2m_core_esp32c3
  //   framework:
  //     version: 3.1.3
  //     advanced:
  //       ignore_efuse_custom_mac: false
  //     source: pioarduino/framework-arduinoespressif32@https:github.com/espressif/arduino-esp32/releases/download/3.1.3/esp32-3.1.3.zip
  //     platform_version: https:github.com/pioarduino/platform-espressif32/releases/download/53.03.13/platform-espressif32.zip
  //     type: arduino
  //   flash_size: 4MB
  //   variant: ESP32C3
  //   cpu_frequency: 160MHZ
  setCpuFrequencyMhz(160);
  // preferences:
  //   id: preferences_intervalsyncer_id
  //   flash_write_interval: 60s
  preferences_intervalsyncer_id = new preferences::IntervalSyncer();
  preferences_intervalsyncer_id->set_write_interval(60000);
  preferences_intervalsyncer_id->set_component_source("preferences");
  App.register_component(preferences_intervalsyncer_id);
  // uart:
  //   id: uart_bus
  //   tx_pin:
  //     number: 0
  //     mode:
  //       output: true
  //       input: false
  //       open_drain: false
  //       pullup: false
  //       pulldown: false
  //     id: esp32_esp32internalgpiopin_id
  //     inverted: false
  //     ignore_pin_validation_error: false
  //     ignore_strapping_warning: false
  //     drive_strength: 20.0
  //   rx_pin:
  //     number: 1
  //     mode:
  //       input: true
  //       output: false
  //       open_drain: false
  //       pullup: false
  //       pulldown: false
  //     id: esp32_esp32internalgpiopin_id_2
  //     inverted: false
  //     ignore_pin_validation_error: false
  //     ignore_strapping_warning: false
  //     drive_strength: 20.0
  //   baud_rate: 9600
  //   data_bits: 8
  //   parity: NONE
  //   stop_bits: 1
  //   rx_buffer_size: 512
  uart_bus = new uart::ESP32ArduinoUARTComponent();
  uart_bus->set_component_source("uart");
  App.register_component(uart_bus);
  uart_bus->set_baud_rate(9600);
  esp32_esp32internalgpiopin_id = new esp32::ESP32InternalGPIOPin();
  esp32_esp32internalgpiopin_id->set_pin(::GPIO_NUM_0);
  esp32_esp32internalgpiopin_id->set_inverted(false);
  esp32_esp32internalgpiopin_id->set_drive_strength(::GPIO_DRIVE_CAP_2);
  esp32_esp32internalgpiopin_id->set_flags(gpio::Flags::FLAG_OUTPUT);
  uart_bus->set_tx_pin(esp32_esp32internalgpiopin_id);
  esp32_esp32internalgpiopin_id_2 = new esp32::ESP32InternalGPIOPin();
  esp32_esp32internalgpiopin_id_2->set_pin(::GPIO_NUM_1);
  esp32_esp32internalgpiopin_id_2->set_inverted(false);
  esp32_esp32internalgpiopin_id_2->set_drive_strength(::GPIO_DRIVE_CAP_2);
  esp32_esp32internalgpiopin_id_2->set_flags(gpio::Flags::FLAG_INPUT);
  uart_bus->set_rx_pin(esp32_esp32internalgpiopin_id_2);
  uart_bus->set_rx_buffer_size(512);
  uart_bus->set_stop_bits(1);
  uart_bus->set_data_bits(8);
  uart_bus->set_parity(uart::UART_CONFIG_PARITY_NONE);
  // sensor.template:
  //   platform: template
  //   name: TVOC
  //   id: voc_tvoc
  //   unit_of_measurement: mg/m³
  //   accuracy_decimals: 3
  //   state_class: measurement
  //   icon: mdi:chemical-weapon
  //   disabled_by_default: false
  //   force_update: false
  //   update_interval: 60s
  voc_tvoc = new template_::TemplateSensor();
  App.register_sensor(voc_tvoc);
  voc_tvoc->set_name("TVOC");
  voc_tvoc->set_object_id("tvoc");
  voc_tvoc->set_disabled_by_default(false);
  voc_tvoc->set_icon("mdi:chemical-weapon");
  voc_tvoc->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  voc_tvoc->set_unit_of_measurement("mg/m\302\263");
  voc_tvoc->set_accuracy_decimals(3);
  voc_tvoc->set_force_update(false);
  voc_tvoc->set_update_interval(60000);
  voc_tvoc->set_component_source("template.sensor");
  App.register_component(voc_tvoc);
  // sensor.template:
  //   platform: template
  //   name: Formaldehyde (CH₂O)
  //   id: voc_hcho
  //   unit_of_measurement: mg/m³
  //   accuracy_decimals: 3
  //   state_class: measurement
  //   icon: mdi:molecule
  //   disabled_by_default: false
  //   force_update: false
  //   update_interval: 60s
  voc_hcho = new template_::TemplateSensor();
  App.register_sensor(voc_hcho);
  voc_hcho->set_name("Formaldehyde (CH\342\202\202O)");
  voc_hcho->set_object_id("formaldehyde__ch_o_");
  voc_hcho->set_disabled_by_default(false);
  voc_hcho->set_icon("mdi:molecule");
  voc_hcho->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  voc_hcho->set_unit_of_measurement("mg/m\302\263");
  voc_hcho->set_accuracy_decimals(3);
  voc_hcho->set_force_update(false);
  voc_hcho->set_update_interval(60000);
  voc_hcho->set_component_source("template.sensor");
  App.register_component(voc_hcho);
  // sensor.template:
  //   platform: template
  //   name: CO2
  //   id: voc_co2
  //   unit_of_measurement: mg/m³
  //   accuracy_decimals: 3
  //   state_class: measurement
  //   icon: mdi:molecule-co2
  //   disabled_by_default: false
  //   force_update: false
  //   update_interval: 60s
  voc_co2 = new template_::TemplateSensor();
  App.register_sensor(voc_co2);
  voc_co2->set_name("CO2");
  voc_co2->set_object_id("co2");
  voc_co2->set_disabled_by_default(false);
  voc_co2->set_icon("mdi:molecule-co2");
  voc_co2->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  voc_co2->set_unit_of_measurement("mg/m\302\263");
  voc_co2->set_accuracy_decimals(3);
  voc_co2->set_force_update(false);
  voc_co2->set_update_interval(60000);
  voc_co2->set_component_source("template.sensor");
  App.register_component(voc_co2);
  // sensor.internal_temperature:
  //   platform: internal_temperature
  //   name: ESP32-C3 Internal Temperature
  //   update_interval: 60s
  //   disabled_by_default: false
  //   force_update: false
  //   id: internal_temperature_internaltemperaturesensor_id
  //   unit_of_measurement: °C
  //   accuracy_decimals: 1
  //   device_class: temperature
  //   state_class: measurement
  //   entity_category: diagnostic
  internal_temperature_internaltemperaturesensor_id = new internal_temperature::InternalTemperatureSensor();
  App.register_sensor(internal_temperature_internaltemperaturesensor_id);
  internal_temperature_internaltemperaturesensor_id->set_name("ESP32-C3 Internal Temperature");
  internal_temperature_internaltemperaturesensor_id->set_object_id("esp32-c3_internal_temperature");
  internal_temperature_internaltemperaturesensor_id->set_disabled_by_default(false);
  internal_temperature_internaltemperaturesensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  internal_temperature_internaltemperaturesensor_id->set_device_class("temperature");
  internal_temperature_internaltemperaturesensor_id->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  internal_temperature_internaltemperaturesensor_id->set_unit_of_measurement("\302\260C");
  internal_temperature_internaltemperaturesensor_id->set_accuracy_decimals(1);
  internal_temperature_internaltemperaturesensor_id->set_force_update(false);
  internal_temperature_internaltemperaturesensor_id->set_update_interval(60000);
  internal_temperature_internaltemperaturesensor_id->set_component_source("internal_temperature.sensor");
  App.register_component(internal_temperature_internaltemperaturesensor_id);
  // interval:
  //   - interval: 2000ms
  //     then:
  //       - lambda: !lambda |-
  //           ESP_LOGI("voc_uart", "⏰ 开始读取21VOC传感器数据");
  //   
  //            清空接收缓冲区中的旧数据 (像C++代码一样)
  //           int cleared = 0;
  //           uint8_t dummy_buffer[1];
  //           while (id(uart_bus).available()) {
  //             if (id(uart_bus).read_array(dummy_buffer, 1) > 0) {
  //               cleared++;
  //             }
  //             delay(1);
  //           }
  //           if (cleared > 0) {
  //             ESP_LOGI("voc_uart", "清空了%d字节旧数据", cleared);
  //           }
  //   
  //            等待数据到达，超时2秒 (像C++代码一样)
  //           uint8_t buffer[64];
  //           int bytesRead = 0;
  //           unsigned long startTime = millis();
  //   
  //           while (bytesRead < 64 && (millis() - startTime) < 2000) {
  //             if (id(uart_bus).available()) {
  //               uint8_t single_byte[1];
  //               if (id(uart_bus).read_array(single_byte, 1) > 0) {
  //                 buffer[bytesRead] = single_byte[0];
  //                 bytesRead++;
  //               }
  //   
  //                如果连续没有新数据超过100ms，认为一帧数据接收完成
  //               unsigned long lastByteTime = millis();
  //               while (!id(uart_bus).available() && (millis() - lastByteTime) < 100) {
  //                 delay(5);
  //               }
  //               if (!id(uart_bus).available()) {
  //                 break;
  //               }
  //             }
  //             delay(10);
  //           }
  //   
  //           if (bytesRead > 0) {
  //              调试：显示接收到的原始数据
  //             ESP_LOGI("voc_uart", "📡 接收到 %d 字节:", bytesRead);
  //             std::string hex_data = "";
  //             for (int i = 0; i < bytesRead; i++) {
  //               char hex_str[4];
  //               sprintf(hex_str, "%02X ", buffer[i]);
  //               hex_data += hex_str;
  //             }
  //             ESP_LOGI("voc_uart", "原始数据: %s", hex_data.c_str());
  //   
  //              解析接收到的数据 - 严格按照C++代码的parseVOCData函数
  //             ESP_LOGI("voc_uart", "🔍 解析数据: 长度=%d", bytesRead);
  //   
  //              检查数据帧长度 (根据文档应该是9字节)
  //             if (bytesRead < 9) {
  //               ESP_LOGW("voc_uart", "数据长度不足（需要9字节，实际%d字节）", bytesRead);
  //               return;
  //             }
  //   
  //              寻找正确的数据帧起始位置 (0x2C模块地址开头)
  //             int frameStart = -1;
  //             for (int i = 0; i <= bytesRead - 9; i++) {
  //               if (buffer[i] == 0x2C && (i + 1 < bytesRead) && buffer[i + 1] == 0xE4) {
  //                 frameStart = i;
  //                 ESP_LOGI("voc_uart", "找到帧头0x2C 0xE4在位置%d", i);
  //                 break;
  //               }
  //             }
  //   
  //             if (frameStart == -1) {
  //               ESP_LOGW("voc_uart", "未找到有效的数据帧头 (0x2C 0xE4)");
  //               return;
  //             }
  //   
  //              检查是否有足够的字节
  //             if (frameStart + 9 > bytesRead) {
  //               ESP_LOGW("voc_uart", "数据帧不完整，需要%d字节，实际只有%d字节", frameStart + 9, bytesRead);
  //               return;
  //             }
  //   
  //              提取9字节数据帧
  //             uint8_t frame[9];
  //             for (int i = 0; i < 9; i++) {
  //               frame[i] = buffer[frameStart + i];
  //             }
  //   
  //              显示数据帧
  //             ESP_LOGI("voc_uart", "数据帧: %02X %02X %02X %02X %02X %02X %02X %02X %02X",
  //                      frame[0], frame[1], frame[2], frame[3], frame[4],
  //                      frame[5], frame[6], frame[7], frame[8]);
  //   
  //              计算校验和 (B1+B2+...+B8的低8位)
  //             uint8_t checksum = 0;
  //             for (int i = 0; i < 8; i++) {
  //               checksum += frame[i];
  //             }
  //             checksum = checksum & 0xFF;  取低8位
  //   
  //              验证校验和
  //             if (frame[8] != checksum) {
  //               ESP_LOGW("voc_uart", "⚠️ 校验和不匹配，但继续解析数据 (接收: 0x%02X, 计算: 0x%02X)", frame[8], checksum);
  //             }
  //   
  //              按照文档协议解析数据
  //              TVOC浓度 (mg/m³): (B3*256 + B4) × 0.001
  //             uint16_t tvoc_raw = frame[2] * 256 + frame[3];
  //             float tvoc_mgm3 = (float)tvoc_raw * 0.001;
  //   
  //              甲醛浓度 (mg/m³): (B5*256 + B6) × 0.001
  //             uint16_t ch2o_raw = frame[4] * 256 + frame[5];
  //             float ch2o_mgm3 = (float)ch2o_raw * 0.001;
  //   
  //              CO₂浓度 (mg/m³): (B7*256 + B8) × 0.001
  //             uint16_t co2_raw = frame[6] * 256 + frame[7];
  //             float co2_mgm3 = (float)co2_raw * 0.001;
  //   
  //             ESP_LOGI("voc_uart", "✅ 解析成功:");
  //             ESP_LOGI("voc_uart", "  🌿 TVOC: %.3f mg/m³", tvoc_mgm3);
  //             ESP_LOGI("voc_uart", "  🏠 甲醛(CH₂O): %.3f mg/m³", ch2o_mgm3);
  //             ESP_LOGI("voc_uart", "  💨 CO₂: %.3f mg/m³", co2_mgm3);
  //   
  //              数据有效性检查 - 严格按照C++代码的validateVOCData函数
  //             bool data_valid = true;
  //   
  //              检查TVOC范围 (0-10 mg/m³)
  //             if (tvoc_mgm3 < 0.0 || tvoc_mgm3 > 10.0) {
  //               ESP_LOGW("voc_uart", "❌ TVOC数值超出范围: %.3f mg/m³", tvoc_mgm3);
  //               data_valid = false;
  //             }
  //   
  //              检查甲醛范围 (0-2 mg/m³)
  //             if (ch2o_mgm3 < 0.0 || ch2o_mgm3 > 2.0) {
  //               ESP_LOGW("voc_uart", "❌ 甲醛数值超出范围: %.3f mg/m³", ch2o_mgm3);
  //               data_valid = false;
  //             }
  //   
  //              检查CO₂范围 (0-10 mg/m³)
  //             if (co2_mgm3 < 0.0 || co2_mgm3 > 10.0) {
  //               ESP_LOGW("voc_uart", "❌ CO₂数值超出范围: %.3f mg/m³", co2_mgm3);
  //               data_valid = false;
  //             }
  //   
  //             if (data_valid) {
  //               ESP_LOGI("voc_uart", "📊 === VOC-CO2-HCHO传感器读数 ===");
  //               ESP_LOGI("voc_uart", "⏰ 时间戳: %lu ms", millis());
  //   
  //                发布TVOC数据
  //               if (id(last_tvoc) == -999.0 || abs(tvoc_mgm3 - id(last_tvoc)) >= 0.001) {
  //                 ESP_LOGI("voc_uart", "✅ 发布TVOC: %.3f mg/m³", tvoc_mgm3);
  //                 id(voc_tvoc).publish_state(tvoc_mgm3);
  //                 id(last_tvoc) = tvoc_mgm3;
  //               }
  //   
  //                发布甲醛数据
  //               if (id(last_hcho) == -999.0 || abs(ch2o_mgm3 - id(last_hcho)) >= 0.001) {
  //                 ESP_LOGI("voc_uart", "✅ 发布甲醛: %.3f mg/m³", ch2o_mgm3);
  //                 id(voc_hcho).publish_state(ch2o_mgm3);
  //                 id(last_hcho) = ch2o_mgm3;
  //               }
  //   
  //                发布CO₂数据
  //               if (id(last_co2) == -999.0 || abs(co2_mgm3 - id(last_co2)) >= 0.001) {
  //                 ESP_LOGI("voc_uart", "✅ 发布CO₂: %.3f mg/m³", co2_mgm3);
  //                 id(voc_co2).publish_state(co2_mgm3);
  //                 id(last_co2) = co2_mgm3;
  //               }
  //   
  //               ESP_LOGI("voc_uart", "===============================");
  //               ESP_LOGI("voc_uart", "✅ 数据读取和处理完成");
  //             } else {
  //               ESP_LOGW("voc_uart", "⚠️ 接收到数据但验证失败");
  //             }
  //           } else {
  //             ESP_LOGW("voc_uart", "⏰ 超时，未接收到数据");
  //           }
  //         type_id: lambdaaction_id_3
  //     trigger_id: trigger_id_3
  //     automation_id: automation_id_3
  //     id: interval_intervaltrigger_id
  //     startup_delay: 0s
  interval_intervaltrigger_id = new interval::IntervalTrigger();
  interval_intervaltrigger_id->set_component_source("interval");
  App.register_component(interval_intervaltrigger_id);
  automation_id_3 = new Automation<>(interval_intervaltrigger_id);
  // text_sensor.wifi_info:
  //   platform: wifi_info
  //   mac_address:
  //     name: Mac Address
  //     disabled_by_default: false
  //     id: wifi_info_macaddresswifiinfo_id
  //     entity_category: diagnostic
  wifi_info_macaddresswifiinfo_id = new wifi_info::MacAddressWifiInfo();
  App.register_text_sensor(wifi_info_macaddresswifiinfo_id);
  wifi_info_macaddresswifiinfo_id->set_name("Mac Address");
  wifi_info_macaddresswifiinfo_id->set_object_id("mac_address");
  wifi_info_macaddresswifiinfo_id->set_disabled_by_default(false);
  wifi_info_macaddresswifiinfo_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_info_macaddresswifiinfo_id->set_component_source("wifi_info.text_sensor");
  App.register_component(wifi_info_macaddresswifiinfo_id);
  // binary_sensor.status:
  //   platform: status
  //   name: Status
  //   disabled_by_default: false
  //   id: status_statusbinarysensor_id
  //   entity_category: diagnostic
  //   device_class: connectivity
  status_statusbinarysensor_id = new status::StatusBinarySensor();
  App.register_binary_sensor(status_statusbinarysensor_id);
  status_statusbinarysensor_id->set_name("Status");
  status_statusbinarysensor_id->set_object_id("status");
  status_statusbinarysensor_id->set_disabled_by_default(false);
  status_statusbinarysensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  status_statusbinarysensor_id->set_device_class("connectivity");
  status_statusbinarysensor_id->set_trigger_on_initial_state(false);
  status_statusbinarysensor_id->set_component_source("status.binary_sensor");
  App.register_component(status_statusbinarysensor_id);
  // switch.restart:
  //   platform: restart
  //   name: Restart
  //   disabled_by_default: false
  //   restore_mode: ALWAYS_OFF
  //   id: restart_restartswitch_id
  //   entity_category: config
  //   icon: mdi:restart
  restart_restartswitch_id = new restart::RestartSwitch();
  App.register_switch(restart_restartswitch_id);
  restart_restartswitch_id->set_name("Restart");
  restart_restartswitch_id->set_object_id("restart");
  restart_restartswitch_id->set_disabled_by_default(false);
  restart_restartswitch_id->set_icon("mdi:restart");
  restart_restartswitch_id->set_entity_category(::ENTITY_CATEGORY_CONFIG);
  restart_restartswitch_id->set_restore_mode(switch_::SWITCH_ALWAYS_OFF);
  restart_restartswitch_id->set_component_source("restart.switch");
  App.register_component(restart_restartswitch_id);
  // md5:
  // socket:
  //   implementation: bsd_sockets
  // globals:
  //   id: last_tvoc
  //   type: float
  //   restore_value: false
  //   initial_value: '-999.0'
  last_tvoc = new globals::GlobalsComponent<float>(-999.0);
  last_tvoc->set_component_source("globals");
  App.register_component(last_tvoc);
  // globals:
  //   id: last_hcho
  //   type: float
  //   restore_value: false
  //   initial_value: '-999.0'
  last_hcho = new globals::GlobalsComponent<float>(-999.0);
  last_hcho->set_component_source("globals");
  App.register_component(last_hcho);
  // globals:
  //   id: last_co2
  //   type: float
  //   restore_value: false
  //   initial_value: '-999.0'
  last_co2 = new globals::GlobalsComponent<float>(-999.0);
  last_co2->set_component_source("globals");
  App.register_component(last_co2);
  lambdaaction_id_3 = new LambdaAction<>([=]() -> void {
      #line 97 "voc-esp32c3.yaml"
      ESP_LOGI("voc_uart", "⏰ 开始读取21VOC传感器数据");
      
       
      int cleared = 0;
      uint8_t dummy_buffer[1];
      while (uart_bus->available()) {
        if (uart_bus->read_array(dummy_buffer, 1) > 0) {
          cleared++;
        }
        delay(1);
      }
      if (cleared > 0) {
        ESP_LOGI("voc_uart", "清空了%d字节旧数据", cleared);
      }
      
       
      uint8_t buffer[64];
      int bytesRead = 0;
      unsigned long startTime = millis();
      
      while (bytesRead < 64 && (millis() - startTime) < 2000) {
        if (uart_bus->available()) {
          uint8_t single_byte[1];
          if (uart_bus->read_array(single_byte, 1) > 0) {
            buffer[bytesRead] = single_byte[0];
            bytesRead++;
          }
      
           
          unsigned long lastByteTime = millis();
          while (!uart_bus->available() && (millis() - lastByteTime) < 100) {
            delay(5);
          }
          if (!uart_bus->available()) {
            break;
          }
        }
        delay(10);
      }
      
      if (bytesRead > 0) {
         
        ESP_LOGI("voc_uart", "📡 接收到 %d 字节:", bytesRead);
        std::string hex_data = "";
        for (int i = 0; i < bytesRead; i++) {
          char hex_str[4];
          sprintf(hex_str, "%02X ", buffer[i]);
          hex_data += hex_str;
        }
        ESP_LOGI("voc_uart", "原始数据: %s", hex_data.c_str());
      
         
        ESP_LOGI("voc_uart", "🔍 解析数据: 长度=%d", bytesRead);
      
         
        if (bytesRead < 9) {
          ESP_LOGW("voc_uart", "数据长度不足（需要9字节，实际%d字节）", bytesRead);
          return;
        }
      
         
        int frameStart = -1;
        for (int i = 0; i <= bytesRead - 9; i++) {
          if (buffer[i] == 0x2C && (i + 1 < bytesRead) && buffer[i + 1] == 0xE4) {
            frameStart = i;
            ESP_LOGI("voc_uart", "找到帧头0x2C 0xE4在位置%d", i);
            break;
          }
        }
      
        if (frameStart == -1) {
          ESP_LOGW("voc_uart", "未找到有效的数据帧头 (0x2C 0xE4)");
          return;
        }
      
         
        if (frameStart + 9 > bytesRead) {
          ESP_LOGW("voc_uart", "数据帧不完整，需要%d字节，实际只有%d字节", frameStart + 9, bytesRead);
          return;
        }
      
         
        uint8_t frame[9];
        for (int i = 0; i < 9; i++) {
          frame[i] = buffer[frameStart + i];
        }
      
         
        ESP_LOGI("voc_uart", "数据帧: %02X %02X %02X %02X %02X %02X %02X %02X %02X",
                 frame[0], frame[1], frame[2], frame[3], frame[4],
                 frame[5], frame[6], frame[7], frame[8]);
      
         
        uint8_t checksum = 0;
        for (int i = 0; i < 8; i++) {
          checksum += frame[i];
        }
        checksum = checksum & 0xFF;  
      
         
        if (frame[8] != checksum) {
          ESP_LOGW("voc_uart", "⚠️ 校验和不匹配，但继续解析数据 (接收: 0x%02X, 计算: 0x%02X)", frame[8], checksum);
        }
      
         
         
        uint16_t tvoc_raw = frame[2] * 256 + frame[3];
        float tvoc_mgm3 = (float)tvoc_raw * 0.001;
      
         
        uint16_t ch2o_raw = frame[4] * 256 + frame[5];
        float ch2o_mgm3 = (float)ch2o_raw * 0.001;
      
         
        uint16_t co2_raw = frame[6] * 256 + frame[7];
        float co2_mgm3 = (float)co2_raw * 0.001;
      
        ESP_LOGI("voc_uart", "✅ 解析成功:");
        ESP_LOGI("voc_uart", "  🌿 TVOC: %.3f mg/m³", tvoc_mgm3);
        ESP_LOGI("voc_uart", "  🏠 甲醛(CH₂O): %.3f mg/m³", ch2o_mgm3);
        ESP_LOGI("voc_uart", "  💨 CO₂: %.3f mg/m³", co2_mgm3);
      
         
        bool data_valid = true;
      
         
        if (tvoc_mgm3 < 0.0 || tvoc_mgm3 > 10.0) {
          ESP_LOGW("voc_uart", "❌ TVOC数值超出范围: %.3f mg/m³", tvoc_mgm3);
          data_valid = false;
        }
      
         
        if (ch2o_mgm3 < 0.0 || ch2o_mgm3 > 2.0) {
          ESP_LOGW("voc_uart", "❌ 甲醛数值超出范围: %.3f mg/m³", ch2o_mgm3);
          data_valid = false;
        }
      
         
        if (co2_mgm3 < 0.0 || co2_mgm3 > 10.0) {
          ESP_LOGW("voc_uart", "❌ CO₂数值超出范围: %.3f mg/m³", co2_mgm3);
          data_valid = false;
        }
      
        if (data_valid) {
          ESP_LOGI("voc_uart", "📊 === VOC-CO2-HCHO传感器读数 ===");
          ESP_LOGI("voc_uart", "⏰ 时间戳: %lu ms", millis());
      
           
          if (last_tvoc->value() == -999.0 || abs(tvoc_mgm3 - last_tvoc->value()) >= 0.001) {
            ESP_LOGI("voc_uart", "✅ 发布TVOC: %.3f mg/m³", tvoc_mgm3);
            voc_tvoc->publish_state(tvoc_mgm3);
            last_tvoc->value() = tvoc_mgm3;
          }
      
           
          if (last_hcho->value() == -999.0 || abs(ch2o_mgm3 - last_hcho->value()) >= 0.001) {
            ESP_LOGI("voc_uart", "✅ 发布甲醛: %.3f mg/m³", ch2o_mgm3);
            voc_hcho->publish_state(ch2o_mgm3);
            last_hcho->value() = ch2o_mgm3;
          }
      
           
          if (last_co2->value() == -999.0 || abs(co2_mgm3 - last_co2->value()) >= 0.001) {
            ESP_LOGI("voc_uart", "✅ 发布CO₂: %.3f mg/m³", co2_mgm3);
            voc_co2->publish_state(co2_mgm3);
            last_co2->value() = co2_mgm3;
          }
      
          ESP_LOGI("voc_uart", "===============================");
          ESP_LOGI("voc_uart", "✅ 数据读取和处理完成");
        } else {
          ESP_LOGW("voc_uart", "⚠️ 接收到数据但验证失败");
        }
      } else {
        ESP_LOGW("voc_uart", "⏰ 超时，未接收到数据");
      }
  });
  automation_id_3->add_actions({lambdaaction_id_3});
  interval_intervaltrigger_id->set_update_interval(2000);
  interval_intervaltrigger_id->set_startup_delay(0);
  // =========== AUTO GENERATED CODE END ============
  App.setup();
}

void loop() {
  App.loop();
}
