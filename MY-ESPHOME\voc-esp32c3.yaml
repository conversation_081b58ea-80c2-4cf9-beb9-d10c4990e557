esphome:
  name: voc-myesp32c3
  # name: voc-esp32c3
  friendly_name: "VOC-CO2-HCHO ESP32-C3 Sensor"

esp32:
  board: airm2m_core_esp32c3
  framework:
    type: arduino

mqtt:
  broker: 11ijEEhVAe.mqtts.acc.cmcconenet.cn   # MQTT服务器地址
  port: 1883              # MQTT端口
  username: 11ijEEhVAe    # MQTT用户名
  password: !secret mqtt_password  # 从secrets.yaml读取MQTT密码
  discovery: true         # 启用Home Assistant自动发现
  discovery_prefix: homeassistant  # HA发现前缀
  topic_prefix: voc_esp32c3  # 设备主题前缀

  # 设备上线/离线消息
  birth_message:
    topic: voc_esp32c3/status
    payload: online
    retain: true
  will_message:
    topic: voc_esp32c3/status
    payload: offline
    retain: true

  # 自定义传感器数据发布
  on_message:
    - topic: voc_esp32c3/command
      then:
        - logger.log:
            format: "收到MQTT命令: %s"
            args: ['x.c_str()']

# 从secrets.yaml文件中读取WiFi配置
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  
  # WiFi连接失败时的处理
  ap:
    ssid: "ESP32C3-VOC Fallback Hotspot"
    password: "12345678"

  # WiFi连接状态回调
  on_connect:
    - logger.log: "WiFi连接成功！"
  on_disconnect:
    - logger.log: "WiFi连接断开！"

# UART配置 - 用于读取VOC-CO2-HCHO传感器数据
uart:
  id: uart_bus
  tx_pin: GPIO0  # TX引脚 - 接传感器RX
  rx_pin: GPIO1  # RX引脚 - 接传感器TX
  baud_rate: 9600  # VOC传感器波特率
  data_bits: 8
  parity: NONE
  stop_bits: 1
  rx_buffer_size: 512  # 增加接收缓冲区大小

# 全局变量存储VOC传感器数据和上次数值
globals:
  - id: last_tvoc
    type: float
    restore_value: no
    initial_value: '-999.0'  # 初始值设为不可能的TVOC浓度
  - id: last_hcho
    type: float
    restore_value: no
    initial_value: '-999.0'  # 初始值设为不可能的甲醛浓度
  - id: last_co2
    type: float
    restore_value: no
    initial_value: '-999.0'  # 初始值设为不可能的CO2浓度

sensor:
  # VOC-CO2-HCHO传感器数据
  - platform: template
    name: "TVOC"
    id: voc_tvoc
    unit_of_measurement: "mg/m³"
    accuracy_decimals: 3
    state_class: "measurement"
    icon: "mdi:chemical-weapon"
    # MQTT发布配置
    on_value:
      then:
        - mqtt.publish:
            topic: voc_esp32c3/sensors/tvoc
            payload: !lambda 'return to_string(x);'
            retain: true

  - platform: template
    name: "Formaldehyde (CH₂O)"
    id: voc_hcho
    unit_of_measurement: "mg/m³"
    accuracy_decimals: 3
    state_class: "measurement"
    icon: "mdi:molecule"
    # MQTT发布配置
    on_value:
      then:
        - mqtt.publish:
            topic: voc_esp32c3/sensors/formaldehyde
            payload: !lambda 'return to_string(x);'
            retain: true

  - platform: template
    name: "CO2"
    id: voc_co2
    unit_of_measurement: "mg/m³"
    accuracy_decimals: 3
    state_class: "measurement"
    icon: "mdi:molecule-co2"
    # MQTT发布配置
    on_value:
      then:
        - mqtt.publish:
            topic: voc_esp32c3/sensors/co2
            payload: !lambda 'return to_string(x);'
            retain: true

  # ESP32-C3内部温度传感器
  - platform: internal_temperature
    name: "ESP32-C3 Internal Temperature"
    update_interval: 60s
    # MQTT发布配置
    on_value:
      then:
        - mqtt.publish:
            topic: voc_esp32c3/sensors/temperature
            payload: !lambda 'return to_string(x);'
            retain: true

# VOC传感器数据处理间隔 - 严格按照test_VOC-CO2-HCHO-Sensor.cpp实现
interval:
  - interval: 2000ms  # 每2秒检查一次UART数据，与C++代码的READ_INTERVAL一致
    then:
      - lambda: |-
          ESP_LOGI("voc_uart", "⏰ 开始读取21VOC传感器数据");

          // 清空接收缓冲区中的旧数据 (像C++代码一样)
          int cleared = 0;
          uint8_t dummy_buffer[1];
          while (id(uart_bus).available()) {
            if (id(uart_bus).read_array(dummy_buffer, 1) > 0) {
              cleared++;
            }
            delay(1);
          }
          if (cleared > 0) {
            ESP_LOGI("voc_uart", "清空了%d字节旧数据", cleared);
          }

          // 等待数据到达，超时2秒 (像C++代码一样)
          uint8_t buffer[64];
          int bytesRead = 0;
          unsigned long startTime = millis();

          while (bytesRead < 64 && (millis() - startTime) < 2000) {
            if (id(uart_bus).available()) {
              uint8_t single_byte[1];
              if (id(uart_bus).read_array(single_byte, 1) > 0) {
                buffer[bytesRead] = single_byte[0];
                bytesRead++;
              }

              // 如果连续没有新数据超过100ms，认为一帧数据接收完成
              unsigned long lastByteTime = millis();
              while (!id(uart_bus).available() && (millis() - lastByteTime) < 100) {
                delay(5);
              }
              if (!id(uart_bus).available()) {
                break;
              }
            }
            delay(10);
          }

          if (bytesRead > 0) {
            // 调试：显示接收到的原始数据
            ESP_LOGI("voc_uart", "📡 接收到 %d 字节:", bytesRead);
            std::string hex_data = "";
            for (int i = 0; i < bytesRead; i++) {
              char hex_str[4];
              sprintf(hex_str, "%02X ", buffer[i]);
              hex_data += hex_str;
            }
            ESP_LOGI("voc_uart", "原始数据: %s", hex_data.c_str());

            // 解析接收到的数据 - 严格按照C++代码的parseVOCData函数
            ESP_LOGI("voc_uart", "🔍 解析数据: 长度=%d", bytesRead);

            // 检查数据帧长度 (根据文档应该是9字节)
            if (bytesRead < 9) {
              ESP_LOGW("voc_uart", "数据长度不足（需要9字节，实际%d字节）", bytesRead);
              return;
            }

            // 寻找正确的数据帧起始位置 (0x2C模块地址开头)
            int frameStart = -1;
            for (int i = 0; i <= bytesRead - 9; i++) {
              if (buffer[i] == 0x2C && (i + 1 < bytesRead) && buffer[i + 1] == 0xE4) {
                frameStart = i;
                ESP_LOGI("voc_uart", "找到帧头0x2C 0xE4在位置%d", i);
                break;
              }
            }

            if (frameStart == -1) {
              ESP_LOGW("voc_uart", "未找到有效的数据帧头 (0x2C 0xE4)");
              return;
            }

            // 检查是否有足够的字节
            if (frameStart + 9 > bytesRead) {
              ESP_LOGW("voc_uart", "数据帧不完整，需要%d字节，实际只有%d字节", frameStart + 9, bytesRead);
              return;
            }

            // 提取9字节数据帧
            uint8_t frame[9];
            for (int i = 0; i < 9; i++) {
              frame[i] = buffer[frameStart + i];
            }

            // 显示数据帧
            ESP_LOGI("voc_uart", "数据帧: %02X %02X %02X %02X %02X %02X %02X %02X %02X",
                     frame[0], frame[1], frame[2], frame[3], frame[4],
                     frame[5], frame[6], frame[7], frame[8]);

            // 计算校验和 (B1+B2+...+B8的低8位)
            uint8_t checksum = 0;
            for (int i = 0; i < 8; i++) {
              checksum += frame[i];
            }
            checksum = checksum & 0xFF; // 取低8位

            // 验证校验和
            if (frame[8] != checksum) {
              ESP_LOGW("voc_uart", "⚠️ 校验和不匹配，但继续解析数据 (接收: 0x%02X, 计算: 0x%02X)", frame[8], checksum);
            }

            // 按照文档协议解析数据
            // TVOC浓度 (mg/m³): (B3*256 + B4) × 0.001
            uint16_t tvoc_raw = frame[2] * 256 + frame[3];
            float tvoc_mgm3 = (float)tvoc_raw * 0.001;

            // 甲醛浓度 (mg/m³): (B5*256 + B6) × 0.001
            uint16_t ch2o_raw = frame[4] * 256 + frame[5];
            float ch2o_mgm3 = (float)ch2o_raw * 0.001;

            // CO₂浓度 (mg/m³): (B7*256 + B8) × 0.001
            uint16_t co2_raw = frame[6] * 256 + frame[7];
            float co2_mgm3 = (float)co2_raw * 0.001;

            ESP_LOGI("voc_uart", "✅ 解析成功:");
            ESP_LOGI("voc_uart", "  🌿 TVOC: %.3f mg/m³", tvoc_mgm3);
            ESP_LOGI("voc_uart", "  🏠 甲醛(CH₂O): %.3f mg/m³", ch2o_mgm3);
            ESP_LOGI("voc_uart", "  💨 CO₂: %.3f mg/m³", co2_mgm3);

            // 数据有效性检查 - 严格按照C++代码的validateVOCData函数
            bool data_valid = true;

            // 检查TVOC范围 (0-10 mg/m³)
            if (tvoc_mgm3 < 0.0 || tvoc_mgm3 > 10.0) {
              ESP_LOGW("voc_uart", "❌ TVOC数值超出范围: %.3f mg/m³", tvoc_mgm3);
              data_valid = false;
            }

            // 检查甲醛范围 (0-2 mg/m³)
            if (ch2o_mgm3 < 0.0 || ch2o_mgm3 > 2.0) {
              ESP_LOGW("voc_uart", "❌ 甲醛数值超出范围: %.3f mg/m³", ch2o_mgm3);
              data_valid = false;
            }

            // 检查CO₂范围 (0-10 mg/m³)
            if (co2_mgm3 < 0.0 || co2_mgm3 > 10.0) {
              ESP_LOGW("voc_uart", "❌ CO₂数值超出范围: %.3f mg/m³", co2_mgm3);
              data_valid = false;
            }

            if (data_valid) {
              ESP_LOGI("voc_uart", "📊 === VOC-CO2-HCHO传感器读数 ===");
              ESP_LOGI("voc_uart", "⏰ 时间戳: %lu ms", millis());

              // 发布TVOC数据
              if (id(last_tvoc) == -999.0 || abs(tvoc_mgm3 - id(last_tvoc)) >= 0.001) {
                ESP_LOGI("voc_uart", "✅ 发布TVOC: %.3f mg/m³", tvoc_mgm3);
                id(voc_tvoc).publish_state(tvoc_mgm3);
                id(last_tvoc) = tvoc_mgm3;
              }

              // 发布甲醛数据
              if (id(last_hcho) == -999.0 || abs(ch2o_mgm3 - id(last_hcho)) >= 0.001) {
                ESP_LOGI("voc_uart", "✅ 发布甲醛: %.3f mg/m³", ch2o_mgm3);
                id(voc_hcho).publish_state(ch2o_mgm3);
                id(last_hcho) = ch2o_mgm3;
              }

              // 发布CO₂数据
              if (id(last_co2) == -999.0 || abs(co2_mgm3 - id(last_co2)) >= 0.001) {
                ESP_LOGI("voc_uart", "✅ 发布CO₂: %.3f mg/m³", co2_mgm3);
                id(voc_co2).publish_state(co2_mgm3);
                id(last_co2) = co2_mgm3;
              }

              // 发送综合JSON数据到MQTT
              std::string json_payload = "{";
              json_payload += "\"tvoc\":" + to_string(tvoc_mgm3) + ",";
              json_payload += "\"formaldehyde\":" + to_string(ch2o_mgm3) + ",";
              json_payload += "\"co2\":" + to_string(co2_mgm3) + ",";
              json_payload += "\"timestamp\":" + to_string(millis()) + ",";
              json_payload += "\"unit\":\"mg/m3\"";
              json_payload += "}";

              // 发布到MQTT
              id(mqtt_client).publish("voc_esp32c3/sensors/all", json_payload, 0, true);
              ESP_LOGI("voc_uart", "📤 发送MQTT数据: %s", json_payload.c_str());

              ESP_LOGI("voc_uart", "===============================");
              ESP_LOGI("voc_uart", "✅ 数据读取和处理完成");
            } else {
              ESP_LOGW("voc_uart", "⚠️ 接收到数据但验证失败");
            }
          } else {
            ESP_LOGW("voc_uart", "⏰ 超时，未接收到数据");
          }

  # VOC传感器不需要发送命令，它会自动发送数据

# 文本传感器
text_sensor:
  # WiFi信息
  - platform: wifi_info
    mac_address:
      name: "Mac Address"

# 二进制传感器
binary_sensor:
  # 设备状态
  - platform: status
    name: "Status"

# 开关控制
switch:
  # 重启开关
  - platform: restart
    name: "Restart"

# 日志配置
logger:
  baud_rate: 115200
  level: INFO  # 显示传感器数据
  logs:
    voc_uart: INFO    # 显示VOC传感器数据
    uart_debug: WARN  # 减少UART调试信息
    uart: WARN        # 减少UART日志
    wifi: WARN        # 减少WiFi日志
    api: WARN         # 减少API日志
    ota: WARN         # 减少OTA日志
    web_server: WARN  # 减少Web服务器日志

# OTA更新
ota:
  platform: esphome

# API配置
api:

# Web服务器
web_server:
  port: 80
  auth:
    username: admin
    password: !secret wifi_password
